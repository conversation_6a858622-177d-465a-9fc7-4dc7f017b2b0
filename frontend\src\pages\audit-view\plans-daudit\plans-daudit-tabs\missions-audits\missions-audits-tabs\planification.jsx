import { useState, useEffect, useRef, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Search, CheckCircle, X } from "lucide-react";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { toast } from "sonner";
import { useApiRequest, withApiErrorHandling } from '@/hooks/useApiRequest';
import { BarChart, UserCheck, User, ChevronUp, ChevronDown, Users, UserPlus, UserX, Crown, DollarSign, Target, Star, AlertTriangle, Filter, Edit } from "lucide-react";

import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { fetchAuditMissionById, clearCurrentMission } from '@/store/slices/audit/auditMissionsSlice';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogT<PERSON>le, DialogTrigger, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import userService from '@/services/userService';
import Chart from 'react-apexcharts';

// Frappe Gantt loader
const loadFrappeGantt = () => {
  return new Promise((resolve, reject) => {
    if (window.Gantt) {
      resolve();
      return;
    }

    // Load CSS first
    const cssLink = document.createElement("link");
    cssLink.rel = "stylesheet";
    cssLink.href = "https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.css";
    document.head.appendChild(cssLink);

    // Then load JS
    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.js";
    script.async = false;
    script.onload = () => {
      if (window.Gantt) resolve();
      else reject(new Error("Frappe Gantt not available after script load"));
    };
    script.onerror = () => reject(new Error("Failed to load Frappe Gantt"));
    document.head.appendChild(script);
  });
};

function PlanificationTab({ missionAudit: propMissionAudit }) {
  const { makeRequest, cancelAllRequests } = useApiRequest();

  // Filters
  const [periode, setPeriode] = useState("Semestre");
  const [dateDebut, setDateDebut] = useState("");
  const [dateFin, setDateFin] = useState("");

  // State management
  const [activities, setActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);

  const ganttRef = useRef(null);
  const ganttInstanceRef = useRef(null);

  // Section open/close states
  const [isGanttOpen, setIsGanttOpen] = useState(true);
  const [isCompetencesOpen, setIsCompetencesOpen] = useState(true);
  const [isEquipeOpen, setIsEquipeOpen] = useState(true);
  const [isInterlocuteurOpen, setIsInterlocuteurOpen] = useState(true);

  // Mission skills state
  const [missionSkills, setMissionSkills] = useState([]);
  const [isLoadingSkills, setIsLoadingSkills] = useState(false);

  // Auditors state
  const [auditors, setAuditors] = useState([]);
  const [selectedAuditor, setSelectedAuditor] = useState(null);
  const [isLoadingAuditors, setIsLoadingAuditors] = useState(false);
  const [auditorSearchTerm, setAuditorSearchTerm] = useState('');

  // Filter state
  const [selectedSkillFilter, setSelectedSkillFilter] = useState('all');
  const [selectedStarFilter, setSelectedStarFilter] = useState('all');

  // Redux and navigation
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { missionAuditId } = useParams();
  const abortControllerRef = useRef(null);
  // Redux state for mission
  const { currentItem: missionAuditRedux, lastFetched } = useSelector(
    (state) => state.auditMissions
  );
  // Use prop if provided, else redux
  const missionAudit = propMissionAudit || missionAuditRedux;
  // --- User selection logic (shared for both fields) ---
  const [users, setUsers] = useState([]);
  const [isAuditedModalOpen, setIsAuditedModalOpen] = useState(false);
  const [pendingInterlocuteurs, setPendingInterlocuteurs] = useState([]);
  const [selectedInterlocuteurs, setSelectedInterlocuteurs] = useState([]); // For Interlocuteurs
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  // --- State for characteristics (for auto-save) ---
  const [initialLoad, setInitialLoad] = useState(true);
  // --- Equipe Intervenante logic (from realisation) ---
  const [equipe, setEquipe] = useState([]);
  const [isLoadingEquipe, setIsLoadingEquipe] = useState(false);
  const [isEquipeDialogOpen, setIsEquipeDialogOpen] = useState(false);
  const [allUsers, setAllUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isAssigning, setIsAssigning] = useState(false);



  // Fetch activities data
  useEffect(() => {
    const fetchActivitiesData = withApiErrorHandling(async () => {
      if (!missionAudit?.id) return;

      try {
        const activitiesResponse = await makeRequest({
          method: 'get',
          url: `${getApiBaseUrl()}/audit-activities/mission/${missionAudit.id}`,
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        }, {
          retries: 2,
          onError: (error) => {
            if (!axios.isCancel(error)) {
              console.error("Error fetching activities:", error);
              toast.error("Erreur lors du chargement des activités");
            }
          }
        });

        if (activitiesResponse && activitiesResponse.data.success) {
          setActivities(activitiesResponse.data.data);
        } else {
          setActivities([]);
        }
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error("Error in activities request:", error);
        }
        setActivities([]);
      }
    }, {
      fallbackValue: null,
      autoRefresh: true,
      refreshDelay: 5000,
      onError: (error) => {
        setActivities([]);
        if (!axios.isCancel(error)) {
          console.error("Critical error fetching activities data:", error);
          toast.error("Erreur lors du chargement des données");
        }
      }
    });

    if (missionAudit?.id) {
      fetchActivitiesData();
    }

    // Cleanup on unmount or dependency change
    return () => {
      cancelAllRequests();
    };
  }, [missionAudit?.id, makeRequest, cancelAllRequests]);
  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoadingUsers(true);
        const response = await axios.get(`${getApiBaseUrl()}/users`);
        if (response.data.success && Array.isArray(response.data.data)) {
          setUsers(response.data.data);
        } else {
          setUsers([]);
          toast.error('Données des utilisateurs non valides');
        }
      } catch (error) {
        console.error('Error loading users:', error);
        setUsers([]);
        toast.error('Erreur lors du chargement des utilisateurs');
      } finally {
        setIsLoadingUsers(false);
      }
    };
    fetchUsers();
  }, []);
  // Fetch mission data with cancellation (if using redux)
  useEffect(() => {
    if (!propMissionAudit) {
      if (abortControllerRef.current) abortControllerRef.current.abort();
      abortControllerRef.current = new AbortController();
      if (missionAuditId && (!missionAudit || missionAudit.id !== missionAuditId || !lastFetched)) {
        dispatch(fetchAuditMissionById({ id: missionAuditId, signal: abortControllerRef.current.signal }))
          .unwrap()
          .catch((error) => {
            if (error && error.name !== 'CanceledError') {
              toast.error(error || 'Erreur lors du chargement de la mission');
              navigate(-1);
            }
          });
      }
      return () => {
        if (abortControllerRef.current) abortControllerRef.current.abort();
        if (!location.pathname.includes('/missions-audits/')) {
          dispatch(clearCurrentMission());
        }
      };
    }
  }, [missionAuditId, dispatch, navigate, missionAudit, lastFetched, location, propMissionAudit]);
  // Update characteristics when mission data changes
  useEffect(() => {
    if (missionAudit && !isLoadingUsers) {
      setSelectedInterlocuteurs(missionAudit.interlocuteurs || []);
    }
  }, [missionAudit, users, isLoadingUsers]);
  useEffect(() => {
    if (missionAudit && !isLoadingUsers && initialLoad) {
      setInitialLoad(false);
    }
  }, [missionAudit, isLoadingUsers, initialLoad]);
  // --- Handlers for user selection ---
  // Multi-select handlers for both sections
  // For dialog selection (pending, not committed until Valider)
  const handleInterlocuteurToggle = (user) => {
    setPendingInterlocuteurs((prev) =>
      prev.some((u) => u.id === user.id)
        ? prev.filter((u) => u.id !== user.id)
        : [...prev, user]
    );
  };
  const handleInterlocuteurRemove = (userId) => {
    setSelectedInterlocuteurs((prev) => prev.filter((u) => u.id !== userId));
  };
  // --- Auto-save logic ---
  // --- Filtered users ---
  const filteredAuditedUsers = users;

  // Helper function to format dates for Gantt
  const formatDateForGantt = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  // Helper function to calculate progress
  const calculateProgress = (activity) => {
    if (activity.chargedetravaileffective && activity.chargedetravailestimee) {
      return Math.min(100, Math.round((activity.chargedetravaileffective / activity.chargedetravailestimee) * 100));
    }
    return activity.status === 'Completed' ? 100 : activity.status === 'In Progress' ? 50 : 0;
  };

  // Gantt rendering
  useEffect(() => {
    if (activities.length === 0) return;

    loadFrappeGantt().then(() => {
      if (!ganttRef.current) return;
      ganttRef.current.innerHTML = "";

      // Filter activities by date if filters are set
      let filtered = activities.filter(activity => {
        const startDate = formatDateForGantt(activity.datedebut);
        const endDate = formatDateForGantt(activity.datefin);

        // Skip activities without valid dates
        if (!startDate || !endDate) return false;

        // Apply date filters
        if (dateDebut && endDate < dateDebut) return false;
        if (dateFin && startDate > dateFin) return false;

        return true;
      });

      // Map to Gantt tasks
      const tasks = filtered.map(activity => ({
        id: activity.id,
        name: "", // Empty name as we show names in the left column
        start: formatDateForGantt(activity.datedebut),
        end: formatDateForGantt(activity.datefin),
        progress: calculateProgress(activity),
        dependencies: "",
        custom_class: `gantt-task-${activity.id}`
      }));

      if (tasks.length === 0) {
        ganttRef.current.innerHTML = '<div class="text-center py-8 text-gray-500">Aucune activité à afficher pour la période sélectionnée</div>';
        return;
      }

      try {
        // Add a small delay to ensure proper DOM rendering
        setTimeout(() => {
          if (ganttRef.current) {
            ganttInstanceRef.current = new window.Gantt(ganttRef.current, tasks, {
              view_mode: periode === "Année" ? "Year" :
                        periode === "Semestre" ? "Month" :
                        periode === "Trimestre" ? "Month" :
                        periode === "Mois" ? "Week" : "Day",
              bar_height: 24,
              padding: 18,
              column_width: periode === "Semestre" ? 60 : periode === "Trimestre" ? 50 : periode === "Mois" ? 40 : 36,
              language: "fr",
              show_label: false,
              readonly: true,
              date_format: "DD/MM/YYYY",
              custom_popup_html: null,
              scroll_to: 'start',
              auto_move_label: false,
              on_click: (task) => {
                const activity = activities.find(a => a.id === task.id);
                if (activity) {
                  setSelectedActivity(activity);
                }
              }
            });

            // Ensure proper positioning and visibility
            setTimeout(() => {
              if (ganttRef.current && ganttInstanceRef.current) {
                const svg = ganttRef.current.querySelector('svg');
                if (svg) {
                  svg.style.display = 'block';
                  svg.style.width = '100%';
                  svg.style.height = 'auto';
                }

                // Allow natural horizontal scrolling - don't force scroll to left
              }
            }, 50);
          }
        }, 100);
      } catch (error) {
        console.error("Error rendering Gantt chart:", error);
        ganttRef.current.innerHTML = '<div class="text-center py-8 text-red-500">Erreur lors du rendu du diagramme de Gantt</div>';
      }
    }).catch(error => {
      console.error("Error loading Frappe Gantt:", error);
      ganttRef.current.innerHTML = '<div class="text-center py-8 text-red-500">Erreur lors du chargement du diagramme de Gantt</div>';
    });
  }, [activities, periode, dateDebut, dateFin]);

  // Helper function to handle activity click
  const handleActivityClick = (activity) => {
    setSelectedActivity(activity);
  };

  // Fetch equipe intervenante for this mission
  const fetchEquipe = useCallback(async () => {
    if (!missionAudit?.id) return;
    setIsLoadingEquipe(true);
    try {
      const response = await axios.get(`${getApiBaseUrl()}/audit-missions/equipe-intervenante?auditMissionId=${missionAudit.id}`, { withCredentials: true });
      if (response.data.success) {
        setEquipe(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching equipe:", error);
      toast.error("Erreur lors du chargement de l'équipe intervenante");
    } finally {
      setIsLoadingEquipe(false);
    }
  }, [missionAudit?.id]);

  // Fetch all users for selection
  const fetchAllUsers = async () => {
    const users = await userService.fetchUsers({ silent: true });
    // Exclude already assigned users
    const assignedIds = equipe.map(e => e.userId);
    setAllUsers(users.filter(u => !assignedIds.includes(u.id)));
  };

  useEffect(() => {
    if (missionAudit?.id) {
      fetchEquipe();
    }
  }, [missionAudit?.id, fetchEquipe]);

  // Add user to equipe
  const handleAssignUser = async (chefdemission = 'non') => {
    if (!selectedUser) return;
    setIsAssigning(true);
    try {
      const response = await axios.post(`${getApiBaseUrl()}/audit-missions/equipe-intervenante`, {
        userId: selectedUser.id,
        auditMissionId: missionAudit.id,
        chefdemission
      }, { withCredentials: true });
      if (response.data.success) {
        toast.success("Membre ajouté à l'équipe");
        setIsEquipeDialogOpen(false);
        setSelectedUser(null);
        fetchEquipe();
      } else {
        toast.error(response.data.message || "Erreur lors de l'ajout");
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Erreur lors de l'ajout");
    } finally {
      setIsAssigning(false);
    }
  };

  // Remove user from equipe
  const handleRemoveUser = async (equipeId) => {
    if (!window.confirm("Retirer ce membre de l'équipe ?")) return;
    try {
      const response = await axios.delete(`${getApiBaseUrl()}/audit-missions/equipe-intervenante/${equipeId}`, { withCredentials: true });
      if (response.data.success) {
        toast.success("Membre retiré");
        fetchEquipe();
      } else {
        toast.error(response.data.message || "Erreur lors du retrait");
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Erreur lors du retrait");
    }
  };

  // Fetch mission skills
  const fetchMissionSkills = useCallback(async () => {
    if (!missionAudit?.id) return;

    try {
      setIsLoadingSkills(true);
      const response = await axios.get(
        `${getApiBaseUrl()}/audit/mission-skills/${missionAudit.id}`,
        { withCredentials: true }
      );

      if (response.data.success) {
        setMissionSkills(response.data.data.requiredSkills || []);
      }
    } catch (error) {
      console.error('Error fetching mission skills:', error);
      if (error.response?.status !== 404) {
        toast.error('Erreur lors du chargement des compétences de la mission');
      }
    } finally {
      setIsLoadingSkills(false);
    }
  }, [missionAudit?.id]);

  // Fetch auditors with their skills
  const fetchAuditors = useCallback(async () => {
    try {
      setIsLoadingAuditors(true);
      const response = await axios.get(
        `${getApiBaseUrl()}/audit/auditors`,
        { withCredentials: true }
      );

      if (response.data.success) {
        setAuditors(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching auditors:', error);
      toast.error('Erreur lors du chargement des auditeurs');
    } finally {
      setIsLoadingAuditors(false);
    }
  }, []);

  // Filter auditors based on search term, skill, and star rating
  const filteredAuditors = auditors.filter(auditor => {
    // Text search filter
    const matchesSearch = auditor.username.toLowerCase().includes(auditorSearchTerm.toLowerCase()) ||
                         auditor.email.toLowerCase().includes(auditorSearchTerm.toLowerCase());

    // Skill filter
    let matchesSkill = true;
    if (selectedSkillFilter && selectedSkillFilter !== 'all') {
      matchesSkill = auditor.auditorSkills?.some(skill =>
        skill.skillId === selectedSkillFilter
      ) || false;
    }

    // Star rating filter
    let matchesStarRating = true;
    if (selectedStarFilter && selectedStarFilter !== 'all' && selectedSkillFilter && selectedSkillFilter !== 'all') {
      const auditorSkill = auditor.auditorSkills?.find(skill =>
        skill.skillId === selectedSkillFilter
      );
      if (auditorSkill) {
        const starRating = parseInt(selectedStarFilter);
        matchesStarRating = auditorSkill.rating >= starRating;
      } else {
        matchesStarRating = false;
      }
    }

    return matchesSearch && matchesSkill && matchesStarRating;
  });



  // Get auditor initials for avatar
  const getAuditorInitials = (auditor) => {
    return auditor.username
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Get auditor skill level for selected skill filter
  const getAuditorSkillLevel = (auditor, skillId) => {
    if (!skillId || !auditor.auditorSkills) return null;
    const skill = auditor.auditorSkills.find(s => s.skillId === skillId);
    return skill ? skill.rating : 0;
  };

  // Clear all filters
  const clearAllFilters = () => {
    setAuditorSearchTerm('');
    setSelectedSkillFilter('all');
    setSelectedStarFilter('all');
  };

  // Load mission skills and auditors when mission changes
  useEffect(() => {
    fetchMissionSkills();
    fetchAuditors();
  }, [missionAudit?.id, fetchMissionSkills, fetchAuditors]);



  // Generate radar chart data with auditor comparison
  const generateRadarChartData = () => {
    if (!missionSkills || missionSkills.length === 0) {
      return {
        series: [],
        options: {
          chart: {
            type: 'radar',
            height: 450,
            toolbar: {
              show: false
            }
          },
          xaxis: {
            categories: []
          },
          yaxis: {
            min: 0,
            max: 5,
            tickAmount: 5
          },
          plotOptions: {
            radar: {
              size: 140,
              polygons: {
                strokeColors: '#e9e9e9',
                fill: {
                  colors: ['#f8f9fa', '#e9ecef']
                }
              }
            }
          },
          colors: ['#3b82f6', '#ef4444'],
          markers: {
            size: 4,
            colors: ['#3b82f6', '#ef4444'],
            strokeColor: '#fff',
            strokeWidth: 2,
          },
          legend: {
            show: true,
            position: 'bottom'
          },
          title: {
            text: 'Comparaison des Compétences',
            align: 'center',
            style: {
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#1f2937'
            }
          }
        }
      };
    }

    // Extract skill names and required levels
    const skillNames = missionSkills.map(skill => skill.skill?.name || 'Compétence inconnue');
    const requiredLevels = missionSkills.map(skill => skill.requiredLevel);

    // Prepare series data
    const series = [
      {
        name: 'Niveau Requis',
        data: requiredLevels
      }
    ];

    // Add auditor skills if an auditor is selected
    if (selectedAuditor && selectedAuditor.auditorSkills) {
      const auditorLevels = missionSkills.map(missionSkill => {
        const auditorSkill = selectedAuditor.auditorSkills.find(
          as => as.skillId === missionSkill.skillId
        );
        return auditorSkill ? auditorSkill.rating : 0;
      });

      series.push({
        name: `${selectedAuditor.username}`,
        data: auditorLevels
      });
    }

    return {
      series: series,
      options: {
        chart: {
          type: 'radar',
          height: 450,
          toolbar: {
            show: false
          }
        },
        xaxis: {
          categories: skillNames,
          labels: {
            style: {
              fontSize: '11px',
              fontWeight: 500
            }
          }
        },
        yaxis: {
          min: 0,
          max: 5,
          tickAmount: 5,
          labels: {
            formatter: function (val) {
              return val.toFixed(0);
            }
          }
        },
        plotOptions: {
          radar: {
            size: 140,
            polygons: {
              strokeColors: '#e9e9e9',
              fill: {
                colors: ['#f8f9fa', '#e9ecef']
              }
            }
          }
        },
        colors: ['#3b82f6', '#ef4444'],
        markers: {
          size: 5,
          colors: ['#3b82f6', '#ef4444'],
          strokeColor: '#fff',
          strokeWidth: 2,
        },
        fill: {
          opacity: 0.25
        },
        legend: {
          show: true,
          position: 'bottom',
          fontSize: '14px'
        },
        title: {
          text: selectedAuditor
            ? `Comparaison: Mission vs ${selectedAuditor.username}`
            : 'Compétences Requises pour la Mission',
          align: 'center',
          style: {
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#1f2937'
          }
        },
        tooltip: {
          y: {
            formatter: function (val, opts) {
              const skillIndex = opts.dataPointIndex;
              const skill = missionSkills[skillIndex];
              const seriesName = opts.series[opts.seriesIndex].name;

              if (seriesName === 'Niveau Requis') {
                return `Niveau requis: ${val}/5${skill?.importance ? ` (${skill.importance})` : ''}`;
              } else {
                return `Niveau actuel: ${val}/5`;
              }
            }
          }
        }
      }
    };
  };



  if (!missionAudit) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations de la mission...</p>
      </div>
    );
  }

  const ganttStyles = `
    .gantt {
      width: 100%;
      height: auto;
      overflow: visible;
    }
    .gantt svg {
      background: white;
      width: 100%;
      height: auto;
    }
    .gantt .grid-background {
      fill: white;
    }
    .gantt .bar {
      fill: #2563eb;
      stroke: #1e40af;
      stroke-width: 1;
    }
    .gantt .bar-progress {
      fill: #60a5fa;
      stroke: #1e40af;
      stroke-width: 1;
    }
    .gantt text {
      fill: #1e293b;
      font-size: 12px;
    }
    .gantt .grid-header, .gantt .grid-row {
      fill: #fff;
      stroke: #e2e8f0;
      stroke-width: 1;
    }
    .gantt .grid-header {
      stroke: #cbd5e1;
      stroke-width: 2;
      fill: #f8fafc;
    }
    .gantt .bar-label {
      display: none;
    }
    .gantt .tick {
      stroke: #e2e8f0;
      stroke-width: 1;
    }
    .gantt .today-highlight {
      fill: rgba(59, 130, 246, 0.1);
    }
    .activity-list {
      width: 200px;
      min-width: 200px;
      border-right: 1px solid #e2e8f0;
      padding-right: 8px;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
    }
    .activity-header {
      height: 62px;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8fafc;
    }
    .activity-item {
      height: 48px;
      display: flex;
      align-items: center;
      padding-left: 8px;
      border-bottom: 1px solid #e2e8f0;
      box-sizing: border-box;
      line-height: 24px;
      cursor: pointer;
      background-color: white;
    }
    .activity-item:hover {
      background-color: #f1f5f9;
    }
    .activity-item.selected {
      background-color: #dbeafe;
    }
    .gantt-container {
      flex: 1;
      overflow-x: auto;
      overflow-y: hidden;
      min-height: 48px;
      width: 100%;
      max-width: calc(100vw - 250px);
    }
    .gantt-wrapper {
      display: flex;
      width: 100%;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      overflow: hidden;
      background: white;
    }
  `;

  // Filter activities for display in the list
  const filteredActivities = activities.filter(activity => {
    const startDate = formatDateForGantt(activity.datedebut);
    const endDate = formatDateForGantt(activity.datefin);

    // Skip activities without valid dates
    if (!startDate || !endDate) return false;

    // Apply date filters
    if (dateDebut && endDate < dateDebut) return false;
    if (dateFin && startDate > dateFin) return false;

    return true;
  });

  return (
    <div className="space-y-6 py-4">
      <style>{ganttStyles}</style>
      {/* Section Gantt */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsGanttOpen(!isGanttOpen)}
        >
          <div className="flex items-center gap-2">
            {isGanttOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <BarChart className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Gantt</span>
          </div>
        </button>
        {isGanttOpen && (
          <div className="p-5 bg-white">
            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Période</label>
                  <Select value={periode} onValueChange={setPeriode}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner une période" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Jour">Jour</SelectItem>
                      <SelectItem value="Mois">Mois</SelectItem>
                      <SelectItem value="Trimestre">Trimestre</SelectItem>
                      <SelectItem value="Semestre">Semestre</SelectItem>
                      <SelectItem value="Année">Année</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date de début</label>
                  <Input
                    type="date"
                    value={dateDebut}
                    onChange={(e) => setDateDebut(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date de fin</label>
                  <Input
                    type="date"
                    value={dateFin}
                    onChange={(e) => setDateFin(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="flex items-end">
                  <button
                    onClick={() => {
                      setDateDebut("");
                      setDateFin("");
                      setPeriode("Semestre");
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Réinitialiser
                  </button>
                </div>
              </div>
            </div>
            {/* Custom Gantt Chart */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                Planification des Activités - {missionAudit.name}
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  {filteredActivities.length} activité{filteredActivities.length > 1 ? 's' : ''} affichée{filteredActivities.length > 1 ? 's' : ''}
                </p>
              </div>

              {filteredActivities.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  Aucune activité à afficher pour la période sélectionnée
                </div>
              ) : (
                <CustomGanttChart
                  activities={filteredActivities}
                  periode={periode}
                  onActivityClick={handleActivityClick}
                  selectedActivity={selectedActivity}
                />
              )}
            </div>
          </div>
        )}
      </div>

      {/* Section Compétences Requises */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg"
          onClick={() => setIsCompetencesOpen(!isCompetencesOpen)}
        >
          <div className="flex items-center gap-2">
            {isCompetencesOpen ? (
              <ChevronUp className="h-5 w-5 text-green-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-green-600" />
            )}
            <UserCheck className="h-5 w-5 text-green-600 mr-1" />
            <span className="text-lg font-medium text-green-800">Compétences Requises</span>
          </div>
        </button>
        {isCompetencesOpen && (
          <div className="p-5 bg-white">
            {isLoadingSkills ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-6 w-6 animate-spin text-green-500" />
                <span className="ml-2 text-gray-600">Chargement des compétences...</span>
              </div>
            ) : missionSkills.length === 0 ? (
              <div className="text-center py-8">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune compétence requise</h3>
                <p className="text-gray-600">
                  Cette mission n'a pas encore de compétences requises définies.
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Compact Skills Summary */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <Target className="h-4 w-4 text-green-600" />
                    Compétences Requises ({missionSkills.length})
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {missionSkills.map((skill, index) => {
                      return (
                        <div key={index} className="flex items-center gap-1 bg-white px-3 py-1 rounded-full border text-sm">
                          <span className="font-medium">{skill.skill?.name}</span>
                          <div className="flex items-center gap-1">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className={`h-3 w-3 ${
                                  star <= skill.requiredLevel
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Main Comparison Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Radar Chart */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      Comparaison des Compétences
                    </h4>
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <Chart
                        options={generateRadarChartData().options}
                        series={generateRadarChartData().series}
                        type="radar"
                        height={450}
                      />
                    </div>
                  </div>

                  {/* Auditor Selection */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        Sélection d'Auditeur
                      </h4>
                      {selectedAuditor && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedAuditor(null)}
                        >
                          <X className="h-4 w-4 mr-1" />
                          Désélectionner
                        </Button>
                      )}
                    </div>

                    {/* Search Bar */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Rechercher un auditeur..."
                        value={auditorSearchTerm}
                        onChange={(e) => setAuditorSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>

                    {/* Filters */}
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-3">
                        <Filter className="h-4 w-4 text-gray-600" />
                        <span className="text-sm font-medium text-gray-700">Filtres</span>
                        {(selectedSkillFilter !== 'all' || selectedStarFilter !== 'all' || auditorSearchTerm) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={clearAllFilters}
                            className="text-xs h-6 px-2"
                          >
                            Réinitialiser
                          </Button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 gap-3">
                        {/* Skill Filter */}
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Compétence
                          </label>
                          <Select value={selectedSkillFilter} onValueChange={setSelectedSkillFilter}>
                            <SelectTrigger className="h-8 text-sm">
                              <SelectValue placeholder="Toutes les compétences" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">Toutes les compétences</SelectItem>
                              {missionSkills.map((skill) => (
                                <SelectItem key={skill.skillId} value={skill.skillId}>
                                  {skill.skill?.name || 'Compétence inconnue'}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Star Rating Filter */}
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Niveau minimum
                          </label>
                          <Select
                            value={selectedStarFilter}
                            onValueChange={setSelectedStarFilter}
                            disabled={selectedSkillFilter === 'all'}
                          >
                            <SelectTrigger className="h-8 text-sm">
                              <SelectValue placeholder="Tous les niveaux" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">Tous les niveaux</SelectItem>
                              <SelectItem value="1">⭐ 1 étoile et plus</SelectItem>
                              <SelectItem value="2">⭐⭐ 2 étoiles et plus</SelectItem>
                              <SelectItem value="3">⭐⭐⭐ 3 étoiles et plus</SelectItem>
                              <SelectItem value="4">⭐⭐⭐⭐ 4 étoiles et plus</SelectItem>
                              <SelectItem value="5">⭐⭐⭐⭐⭐ 5 étoiles</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    {/* Auditors List */}
                    <div className="bg-white border border-gray-200 rounded-lg max-h-96 overflow-y-auto">
                      {isLoadingAuditors ? (
                        <div className="flex items-center justify-center p-8">
                          <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                          <span className="ml-2 text-gray-600">Chargement des auditeurs...</span>
                        </div>
                      ) : filteredAuditors.length === 0 ? (
                        <div className="text-center p-8">
                          <User className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-600">Aucun auditeur trouvé</p>
                        </div>
                      ) : (
                        <div className="divide-y divide-gray-200">
                          {filteredAuditors.map((auditor) => (
                            <div
                              key={auditor.id}
                              onClick={() => setSelectedAuditor(auditor)}
                              className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                                selectedAuditor?.id === auditor.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <Avatar className="h-10 w-10">
                                  <AvatarFallback className="bg-blue-100 text-blue-600">
                                    {getAuditorInitials(auditor)}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1">
                                  <h5 className="font-medium text-gray-900">{auditor.username}</h5>
                                  <p className="text-sm text-gray-500">{auditor.email}</p>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Badge variant="outline" className="text-xs">
                                      {auditor.role}
                                    </Badge>
                                    <span className="text-xs text-gray-500">
                                      {auditor.auditorSkills?.length || 0} compétences
                                    </span>
                                    {selectedSkillFilter && selectedSkillFilter !== 'all' && (
                                      <div className="flex items-center gap-1">
                                        <span className="text-xs text-gray-500">•</span>
                                        <div className="flex items-center gap-1">
                                          {[1, 2, 3, 4, 5].map((star) => (
                                            <Star
                                              key={star}
                                              className={`h-3 w-3 ${
                                                star <= getAuditorSkillLevel(auditor, selectedSkillFilter)
                                                  ? 'text-yellow-400 fill-current'
                                                  : 'text-gray-300'
                                              }`}
                                            />
                                          ))}
                                          <span className="text-xs text-gray-600 ml-1">
                                            {getAuditorSkillLevel(auditor, selectedSkillFilter)}/5
                                          </span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                {selectedAuditor?.id === auditor.id && (
                                  <CheckCircle className="h-5 w-5 text-blue-500" />
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>




      {/* Section Équipe intervenante */}
      <div className="border rounded-lg shadow-sm">
        <div className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg">
          <div className="flex items-center justify-between p-4">
            <button
              type="button"
              className="flex items-center gap-2 flex-1"
              onClick={() => setIsEquipeOpen(!isEquipeOpen)}
            >
              {isEquipeOpen ? (
                <ChevronUp className="h-5 w-5 text-purple-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-purple-600" />
              )}
              <Users className="h-5 w-5 text-purple-600 mr-1" />
              <span className="text-lg font-medium text-purple-800">Équipe intervenante</span>
            </button>
            <Button
              variant="outline"
              onClick={() => {
                setIsEquipeDialogOpen(true);
                fetchAllUsers();
              }}
              className="flex items-center gap-2"
            >
              <UserPlus className="h-4 w-4" />
              Ajouter un membre
            </Button>
          </div>
        </div>
          <div className="p-5 bg-white">
            {isLoadingEquipe ? (
              <div className="flex items-center justify-center h-24">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                <span className="ml-2">Chargement de l'équipe...</span>
              </div>
            ) : equipe.length === 0 ? (
              <p className="text-sm text-gray-500 py-4">Aucun membre assigné à cette mission.</p>
            ) : (
              <div className="flex flex-wrap gap-4">
                {equipe.map(member => (
                  <div key={member.id} className="flex items-center gap-3 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2 shadow-sm relative">
                    <div className="w-9 h-9 rounded-full bg-purple-100 flex items-center justify-center text-purple-700 font-bold text-lg">
                      {member.user?.username?.charAt(0) || member.user?.email?.charAt(0) || 'U'}
                    </div>
                    <div>
                      <div className="font-medium text-gray-800 flex items-center gap-2">
                        {member.user?.username || member.user?.email || 'Utilisateur'}
                        {member.chefdemission === 'oui' && (
                          <span title="Chef de mission" className="ml-1"><Crown className="h-4 w-4 text-yellow-500 inline" /></span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">{member.user?.email}</div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 text-red-600 hover:text-red-800 hover:bg-red-50"
                      onClick={() => handleRemoveUser(member.id)}
                    >
                      <UserX className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>


        {/* Dialog for adding a member */}
        <Dialog open={isEquipeDialogOpen} onOpenChange={setIsEquipeDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Ajouter un membre à l'équipe</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p className="mb-4 text-sm text-gray-600">Sélectionnez un utilisateur à ajouter à l'équipe intervenante pour cette mission :</p>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {allUsers.length === 0 ? (
                  <p className="text-sm text-gray-500">Aucun utilisateur disponible</p>
                ) : allUsers.map(user => (
                  <div
                    key={user.id}
                    onClick={() => setSelectedUser(user)}
                    className={`p-2 rounded-md cursor-pointer flex items-center gap-3 ${selectedUser && selectedUser.id === user.id ? 'bg-purple-100 text-purple-800' : 'hover:bg-gray-100'}`}
                  >
                    <div className="w-8 h-8 rounded-full bg-purple-200 flex items-center justify-center text-purple-700 font-bold">
                      {user.username?.charAt(0) || user.email?.charAt(0) || 'U'}
                    </div>
                    <div>
                      <p className="font-medium">{user.username || user.email}</p>
                      <p className="text-xs text-gray-500">{user.email}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEquipeDialogOpen(false)} disabled={isAssigning}>Annuler</Button>
              <Button onClick={() => handleAssignUser('non')} disabled={!selectedUser || isAssigning}>
                {isAssigning ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <UserPlus className="h-4 w-4 mr-1" />}
                Ajouter
              </Button>
              <Button onClick={() => handleAssignUser('oui')} disabled={!selectedUser || isAssigning || equipe.some(e => e.chefdemission === 'oui')} className="ml-2">
                {isAssigning ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Crown className="h-4 w-4 mr-1 text-yellow-500" />}
                Ajouter comme chef de mission
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      {/* Section Interlocuteur */}
      <div className="border rounded-lg shadow-sm mb-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-rose-50 to-red-50 rounded-t-lg"
          onClick={() => setIsInterlocuteurOpen(!isInterlocuteurOpen)}
        >
          <div className="flex items-center gap-2">
            {isInterlocuteurOpen ? (
              <ChevronUp className="h-5 w-5 text-rose-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-rose-600" />
            )}
            <User className="h-5 w-5 text-rose-600 mr-1" />
            <span className="text-lg font-medium text-rose-800">Interlocuteurs</span>
          </div>
        </button>
        {isInterlocuteurOpen && (
          <div className="p-5 bg-white">
            <div className="max-w-xl">
              <Label className="text-base font-semibold mb-1 block">Interlocuteurs</Label>
              <p className="text-xs text-gray-500 mb-3">Ajoutez plusieurs interlocuteurs pour cette mission.</p>
              <div className="overflow-x-auto flex gap-3 mt-2 mb-4 pb-2">
                {selectedInterlocuteurs.length === 0 && (
                  <span className="text-gray-400 text-sm">Aucun interlocuteur sélectionné</span>
                )}
                {selectedInterlocuteurs.map((user) => (
                  <div key={user.id} className="relative min-w-[180px] max-w-[220px] bg-white border border-rose-100 rounded-lg shadow hover:shadow-md transition p-3 flex flex-col items-center justify-center">
                    <div className="absolute top-1 right-1">
                      <button
                        type="button"
                        className="text-rose-400 hover:text-red-600 focus:outline-none"
                        onClick={() => handleInterlocuteurRemove(user.id)}
                        aria-label="Retirer cet interlocuteur"
                        style={{ background: 'none', border: 'none', cursor: 'pointer' }}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-rose-200 flex items-center justify-center text-lg font-bold text-rose-700 mb-2">
                      {user.username ? user.username[0].toUpperCase() : (user.email ? user.email[0].toUpperCase() : '?')}
                    </div>
                    <div className="text-sm font-medium text-rose-900 truncate w-full text-center">{user.username || user.email}</div>
                    <div className="text-xs text-gray-500 truncate w-full text-center">{user.email}</div>
                  </div>
                ))}
              </div>
              <Dialog open={isAuditedModalOpen} onOpenChange={(open) => {
                setIsAuditedModalOpen(open);
                if (open) setPendingInterlocuteurs(selectedInterlocuteurs);
              }}>
                <DialogTrigger asChild>
                  <Button variant="outline" type="button" className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4 mr-1" /> Ajouter des interlocuteurs
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Sélectionner les interlocuteurs</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Rechercher un utilisateur..."
                        className="pl-10"
                        disabled
                      />
                    </div>
                    <div className="max-h-60 overflow-y-auto space-y-2">
                      {isLoadingUsers ? (
                        <div className="flex items-center justify-center py-8">
                          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600" />
                        </div>
                      ) : filteredAuditedUsers.length > 0 ? (
                        filteredAuditedUsers.map(user => (
                          <div
                            key={user.id}
                            className={`flex items-center gap-3 p-3 border rounded-lg hover:bg-rose-50 cursor-pointer transition ${pendingInterlocuteurs.some(u => u.id === user.id) ? 'bg-rose-100 border-rose-300' : ''}`}
                            onClick={() => handleInterlocuteurToggle(user)}
                          >
                            <input
                              type="checkbox"
                              checked={pendingInterlocuteurs.some(u => u.id === user.id)}
                              readOnly
                              className="accent-rose-600"
                            />
                            <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <User className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <p className="font-medium text-sm">{user.username}</p>
                              <p className="text-xs text-gray-500">{user.email}</p>
                            </div>
                          </div>
                        ))
                      ) : (
                        <p className="text-center text-gray-500 py-4">
                          {/* No users available */}
                        </p>
                      )}
                    </div>
                    <div className="flex justify-end gap-2 pt-2">
                      <Button variant="outline" onClick={() => setIsAuditedModalOpen(false)}>
                        Annuler
                      </Button>
                      <Button
                        variant="default"
                        className="bg-rose-600 hover:bg-rose-700 text-white"
                        onClick={() => {
                          setSelectedInterlocuteurs(pendingInterlocuteurs);
                          setIsAuditedModalOpen(false);
                        }}
                      >
                        Valider
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Custom Gantt Chart Component
const CustomGanttChart = ({ activities, periode, onActivityClick, selectedActivity }) => {
  const [showActivityModal, setShowActivityModal] = useState(false);
  const [modalActivity, setModalActivity] = useState(null);

  // Helper function to format dates
  const formatDateForGantt = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  // Helper function to calculate progress
  const calculateProgress = (activity) => {
    if (activity.chargedetravaileffective && activity.chargedetravailestimee) {
      return Math.min(100, Math.round((activity.chargedetravaileffective / activity.chargedetravailestimee) * 100));
    }
    return activity.status === 'Completed' ? 100 : activity.status === 'In Progress' ? 50 : 0;
  };

  // Handle activity click to show modal
  const handleActivityClick = (activity) => {
    setModalActivity(activity);
    setShowActivityModal(true);
    onActivityClick(activity);
  };

  // Generate timeline dates based on period
  const generateTimeline = () => {
    if (activities.length === 0) return [];

    // Find min and max dates
    const dates = activities
      .map(activity => [
        formatDateForGantt(activity.datedebut),
        formatDateForGantt(activity.datefin)
      ])
      .flat()
      .filter(Boolean)
      .map(date => new Date(date));

    if (dates.length === 0) return [];

    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));

    // Adjust start date to beginning of period
    const startDate = new Date(minDate);
    const endDate = new Date(maxDate);

    switch (periode) {
      case 'Jour':
        // Start from the beginning of the month for context
        startDate.setDate(1);
        break;
      case 'Mois':
        // Start from the beginning of the month
        startDate.setDate(1);
        // Extend to end of month for the last activity
        endDate.setMonth(endDate.getMonth() + 1);
        endDate.setDate(0); // Last day of previous month
        break;
      case 'Trimestre':
      case 'Semestre':
        // Start from beginning of the month
        startDate.setDate(1);
        // Extend a bit for better view
        endDate.setMonth(endDate.getMonth() + 2);
        break;
      case 'Année':
        // Start from beginning of the year
        startDate.setMonth(0, 1);
        // Extend to end of year
        endDate.setMonth(11, 31);
        break;
    }

    // Generate timeline based on period
    const timeline = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      timeline.push(new Date(current));

      switch (periode) {
        case 'Jour':
          current.setDate(current.getDate() + 1);
          break;
        case 'Mois':
          current.setDate(current.getDate() + 1); // Daily for month view
          break;
        case 'Trimestre':
          current.setMonth(current.getMonth() + 1); // Monthly for trimestre view
          break;
        case 'Semestre':
          current.setMonth(current.getMonth() + 1); // Monthly for semestre
          break;
        case 'Année':
          current.setMonth(current.getMonth() + 3); // Quarterly for year
          break;
        default:
          current.setDate(current.getDate() + 1);
      }
    }

    return timeline;
  };

  // Format timeline header labels
  const formatTimelineLabel = (date) => {
    switch (periode) {
      case 'Jour':
        return date.getDate();
      case 'Mois':
        return date.getDate();
      case 'Trimestre':
        return date.toLocaleDateString('fr-FR', { month: 'short' });
      case 'Semestre':
        return date.toLocaleDateString('fr-FR', { month: 'short' });
      case 'Année':
        return `T${Math.ceil((date.getMonth() + 1) / 3)}`;
      default:
        return date.getDate();
    }
  };



  // Generate comprehensive headers for better context
  const generateHeaders = (timeline) => {
    const headers = [];

    switch (periode) {
      case 'Jour':
      case 'Mois': {
        // Month headers
        let currentMonth = null;
        let monthStart = 0;

        timeline.forEach((date, index) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          const monthKey = `${year}-${month}`;

          if (currentMonth !== monthKey) {
            if (currentMonth !== null) {
              headers.push({
                label: new Date(timeline[monthStart]).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }),
                start: monthStart,
                width: index - monthStart,
                type: 'month'
              });
            }
            currentMonth = monthKey;
            monthStart = index;
          }
        });

        // Add the last month
        if (currentMonth !== null) {
          headers.push({
            label: new Date(timeline[monthStart]).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }),
            start: monthStart,
            width: timeline.length - monthStart,
            type: 'month'
          });
        }
        break;
      }

      case 'Trimestre': {
        // Year headers for trimestre (since we show months, we group by year)
        let currentTrimestreYear = null;
        let yearStart = 0;

        timeline.forEach((date, index) => {
          const year = date.getFullYear();

          if (currentTrimestreYear !== year) {
            if (currentTrimestreYear !== null) {
              headers.push({
                label: currentTrimestreYear.toString(),
                start: yearStart,
                width: index - yearStart,
                type: 'year'
              });
            }
            currentTrimestreYear = year;
            yearStart = index;
          }
        });

        if (currentTrimestreYear !== null) {
          headers.push({
            label: currentTrimestreYear.toString(),
            start: yearStart,
            width: timeline.length - yearStart,
            type: 'year'
          });
        }
        break;
      }

      case 'Semestre': {
        // Year headers for semestre
        let currentSemestreYear = null;
        let yearStart = 0;

        timeline.forEach((date, index) => {
          const year = date.getFullYear();

          if (currentSemestreYear !== year) {
            if (currentSemestreYear !== null) {
              headers.push({
                label: currentSemestreYear.toString(),
                start: yearStart,
                width: index - yearStart,
                type: 'year'
              });
            }
            currentSemestreYear = year;
            yearStart = index;
          }
        });

        if (currentSemestreYear !== null) {
          headers.push({
            label: currentSemestreYear.toString(),
            start: yearStart,
            width: timeline.length - yearStart,
            type: 'year'
          });
        }
        break;
      }

      case 'Année': {
        // Year headers for année
        let currentAnneeYear = null;
        let anneeYearStart = 0;

        timeline.forEach((date, index) => {
          const year = date.getFullYear();

          if (currentAnneeYear !== year) {
            if (currentAnneeYear !== null) {
              headers.push({
                label: currentAnneeYear.toString(),
                start: anneeYearStart,
                width: index - anneeYearStart,
                type: 'year'
              });
            }
            currentAnneeYear = year;
            anneeYearStart = index;
          }
        });

        if (currentAnneeYear !== null) {
          headers.push({
            label: currentAnneeYear.toString(),
            start: anneeYearStart,
            width: timeline.length - anneeYearStart,
            type: 'year'
          });
        }
        break;
      }
    }

    return headers;
  };

  // Calculate position and width for activity bars
  const calculateBarPosition = (activity, timeline) => {
    const startDate = new Date(formatDateForGantt(activity.datedebut));
    const endDate = new Date(formatDateForGantt(activity.datefin));
    const timelineStart = timeline[0];
    const timelineEnd = timeline[timeline.length - 1];

    if (!startDate || !endDate) return null;

    const totalDuration = timelineEnd - timelineStart;
    const activityStart = startDate - timelineStart;
    const activityDuration = endDate - startDate;

    const left = (activityStart / totalDuration) * 100;
    const width = (activityDuration / totalDuration) * 100;

    return { left: Math.max(0, left), width: Math.max(1, width) };
  };

  const timeline = generateTimeline();
  const headers = generateHeaders(timeline);
  const cellWidth = periode === 'Jour' ? 30 : periode === 'Mois' ? 25 : periode === 'Trimestre' ? 40 : periode === 'Semestre' ? 60 : 80;

  if (timeline.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Aucune activité avec des dates valides
      </div>
    );
  }

  return (
    <>
      <div className="custom-gantt-container">
        <div className="flex">
          {/* Activity Names Column */}
          <div className="custom-gantt-activities" style={{ minWidth: '200px', maxWidth: '200px' }}>
            {/* Context Header (if applicable) */}
            {headers.length > 0 && (
              <div className="custom-gantt-context-header" style={{ height: '30px', borderBottom: '1px solid #e2e8f0', background: '#f1f5f9', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', fontWeight: '600', color: '#64748b' }}>
                {headers[0]?.type === 'month' ? 'Mois' : 'Année'}
              </div>
            )}
            {/* Header */}
            <div className="custom-gantt-header" style={{ height: '40px', borderBottom: '2px solid #e2e8f0', background: '#f8fafc', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: '600', fontSize: '14px' }}>
              Activités
            </div>
            {/* Activity Rows */}
            {activities.map((activity, index) => (
              <div
                key={activity.id}
                className={`custom-gantt-activity-row ${selectedActivity?.id === activity.id ? 'selected' : ''}`}
                style={{
                  height: '40px',
                  borderBottom: '1px solid #e2e8f0',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '0 8px',
                  cursor: 'pointer',
                  backgroundColor: selectedActivity?.id === activity.id ? '#dbeafe' : index % 2 === 0 ? '#ffffff' : '#f9fafb'
                }}
                onClick={() => handleActivityClick(activity)}
                title={activity.name}
              >
                <span className="text-sm text-gray-900 truncate">{activity.name}</span>
              </div>
            ))}
          </div>

          {/* Timeline and Bars */}
          <div className="custom-gantt-timeline" style={{ flex: 1, overflowX: 'auto', borderLeft: '1px solid #e2e8f0' }}>
            {/* Context Headers (if applicable) */}
            {headers.length > 0 && (
              <div className="custom-gantt-context-headers" style={{ height: '30px', borderBottom: '1px solid #e2e8f0', background: '#f1f5f9', display: 'flex', minWidth: `${timeline.length * cellWidth}px` }}>
                {headers.map((header, index) => (
                  <div
                    key={index}
                    className="custom-gantt-context-cell"
                    style={{
                      width: `${header.width * cellWidth}px`,
                      borderRight: '1px solid #cbd5e1',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '11px',
                      fontWeight: '600',
                      color: '#64748b',
                      textTransform: 'capitalize'
                    }}
                  >
                    {header.label}
                  </div>
                ))}
              </div>
            )}

            {/* Timeline Header */}
            <div className="custom-gantt-timeline-header" style={{ height: '40px', borderBottom: '2px solid #e2e8f0', background: '#f8fafc', display: 'flex', minWidth: `${timeline.length * cellWidth}px` }}>
              {timeline.map((date, index) => (
                <div
                  key={index}
                  className="custom-gantt-timeline-cell"
                  style={{
                    width: `${cellWidth}px`,
                    borderRight: '1px solid #e2e8f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}
                >
                  {formatTimelineLabel(date)}
                </div>
              ))}
            </div>

            {/* Activity Bars */}
            <div className="custom-gantt-bars" style={{ position: 'relative', minWidth: `${timeline.length * cellWidth}px` }}>
              {activities.map((activity, index) => {
                const barPosition = calculateBarPosition(activity, timeline);
                if (!barPosition) return null;

                const progress = calculateProgress(activity);

                return (
                  <div
                    key={activity.id}
                    className="custom-gantt-bar-row"
                    style={{
                      height: '40px',
                      borderBottom: '1px solid #e2e8f0',
                      position: 'relative',
                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#f9fafb'
                    }}
                  >
                    {/* Activity Bar */}
                    <div
                      className="custom-gantt-bar"
                      style={{
                        position: 'absolute',
                        left: `${(barPosition.left / 100) * timeline.length * cellWidth}px`,
                        width: `${Math.max(20, (barPosition.width / 100) * timeline.length * cellWidth)}px`,
                        height: '24px',
                        top: '8px',
                        backgroundColor: selectedActivity?.id === activity.id ? '#3b82f6' : '#6366f1',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        overflow: 'hidden'
                      }}
                      onClick={() => handleActivityClick(activity)}
                      title={`${activity.name} (${progress}%)`}
                    >
                      {/* Progress Bar */}
                      <div
                        style={{
                          width: `${progress}%`,
                          height: '100%',
                          backgroundColor: selectedActivity?.id === activity.id ? '#1d4ed8' : '#4f46e5',
                          borderRadius: '4px 0 0 4px'
                        }}
                      />
                      {/* Activity Name (if space allows) */}
                      <span
                        style={{
                          position: 'absolute',
                          left: '4px',
                          fontSize: '11px',
                          color: 'white',
                          fontWeight: '500',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          maxWidth: 'calc(100% - 8px)'
                        }}
                      >
                        {activity.name}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Activity Details Modal */}
      {showActivityModal && modalActivity && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={() => setShowActivityModal(false)}>
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">Détails de l'activité</h3>
                <button
                  onClick={() => setShowActivityModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nom de l'activité</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalActivity.name}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalActivity.description || 'Non renseignée'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {modalActivity.datedebut ? new Date(modalActivity.datedebut).toLocaleDateString('fr-FR') : 'Non définie'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {modalActivity.datefin ? new Date(modalActivity.datefin).toLocaleDateString('fr-FR') : 'Non définie'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Charge estimée</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalActivity.chargedetravailestimee || 'Non renseignée'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Charge effective</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalActivity.chargedetravaileffective || 'Non renseignée'}</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Progression</label>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-900">{calculateProgress(modalActivity)}% complété</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${calculateProgress(modalActivity)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              <div className="flex justify-end">
                <button
                  onClick={() => setShowActivityModal(false)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Fermer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


    </>
  );
};

export default PlanificationTab;
