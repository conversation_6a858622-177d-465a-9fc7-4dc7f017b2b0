import React, { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react";
import axios from "axios";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { DateInput } from "@/components/ui/date-input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Save,
  Loader2,
  AlertCircle,
  CheckCircle2,
  ArrowLeft,
  ArrowRight,
  Download,
  Upload,
  X,
  CheckIcon,
  ChevronUp,
  ChevronDown,
  File,
  Trash2 as Trash,
  <PERSON><PERSON><PERSON><PERSON>,
  ExternalLink,
  Link,
  Plus
} from "lucide-react";
import { useCustomOutletContext } from "../../edit-fiches-travail";
import { 
  getResponsesByFicheId,
  upsertResponse
} from "@/services/fiche-test-response-service";
import { toast } from "sonner";
import { debounce } from "lodash";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { getApiBaseUrl } from "@/utils/api-config";

// Separate component for multi-select to isolate hooks
const MultiSelect = ({ question, sampleNumber, value, onAnswerChange }) => {
  const selectedValues = Array.isArray(value) ? value : (value ? [value] : []);
  const [open, setOpen] = useState(false); // State to control dropdown open/close

  const handleSelectToggle = (option) => {
    let newValues;
    if (selectedValues.includes(option)) {
      // Remove if already selected
      newValues = selectedValues.filter(v => v !== option);
    } else {
      // Add if not selected
      newValues = [...selectedValues, option];
    }
    onAnswerChange(sampleNumber, question.id, newValues);
  };

  return (
    <Select open={open} onOpenChange={setOpen}>
      <SelectTrigger className="w-full text-xs h-8">
        {selectedValues.length > 0 ? (
          <span className="text-gray-700">
            {selectedValues.length} item(s) sélectionné(s)
          </span>
        ) : (
          <span className="text-gray-500">Sélectionnez</span>
        )}
      </SelectTrigger>
      <SelectContent>
        {question.options?.map((option, optIndex) => {
          const isSelected = selectedValues.includes(option);
          return (
            <div
              key={optIndex}
              className="relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              onClick={() => handleSelectToggle(option)}
              onMouseDown={(e) => e.preventDefault()} // Keep dropdown open on click
            >
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => handleSelectToggle(option)}
                className="mr-2"
              />
              <span className="flex-1">{option}</span>
            </div>
          );
        })}
      </SelectContent>
    </Select>
  );
};

function FichesTestTab() {
  const context = useCustomOutletContext();
  const fiche = context?.fiche || null;
  const abortControllerRef = useRef(null);

  const [isLoading, setIsLoading] = useState(false);
  const [responses, setResponses] = useState({});
  const [questions, setQuestions] = useState([]);
  const [isHeaderOpen, setIsHeaderOpen] = useState(true);
  // New states for attachments and references
  const [businessDocuments, setBusinessDocuments] = useState({});
  const [externalReferences, setExternalReferences] = useState({});
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState(false);
  const [newReference, setNewReference] = useState({ url: "", description: "", questionId: null, sampleNumber: null });
  const API_BASE_URL = getApiBaseUrl();

  const sampleSize = useMemo(() => {
    if (!fiche?.tailleEchantillon) return 0;
    const size = parseInt(fiche.tailleEchantillon);
    return isNaN(size) ? 0 : Math.min(size, 99);
  }, [fiche?.tailleEchantillon]);

  const sampleNumbers = useMemo(() => {
    return Array.from({ length: sampleSize }, (_, i) => i + 1);
  }, [sampleSize]);

  const debouncedSave = useCallback(
    debounce(async (sampleNumber, questionId, answer) => {
      if (!fiche?.id) return;
      
      try {
        const response = await upsertResponse({
          ficheDeTravailID: fiche.id,
          questionID: questionId,
          sampleNumber,
          answer
        });

        if (response && response.success) {
          // Success - no UI feedback needed
        } else {
          toast.error("Erreur lors de la sauvegarde");
        }
      } catch (error) {
        console.error('Error saving response:', error);
        toast.error("Erreur lors de la sauvegarde");
      }
    }, 1000),
    [fiche?.id]
  );

  // Refactored fetchData to handle existing responses
  const fetchData = useCallback(async () => {
    if (!fiche?.id) return;

    setIsLoading(true);
    try {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();

      if (fiche.questions) {
        setQuestions(fiche.questions.sort((a, b) => a.order_index - b.order_index));
      }

      const response = await getResponsesByFicheId(fiche.id, abortControllerRef.current.signal);
        if (response && response.success) {
        const responsesMap = {};
        response.data.forEach(resp => {
          if (!responsesMap[resp.sampleNumber]) {
            responsesMap[resp.sampleNumber] = {};
          }

          let answerValue = resp.answer;
          if (resp.question?.input_type === 'multi-select' || resp.question?.input_type === 'checkbox') {
            if (typeof answerValue === 'string') {
              try {
                answerValue = JSON.parse(answerValue);
              } catch (_e) {
                answerValue = answerValue ? [answerValue] : [];
              }
            }
            if (!Array.isArray(answerValue)) {
              answerValue = answerValue ? [answerValue] : [];
            }
          }

          responsesMap[resp.sampleNumber][resp.questionID] = answerValue;
        });
        setResponses(responsesMap);
        }
      } catch (error) {
        if (error.name !== 'CanceledError') {
        console.error('Error fetching data:', error);
        toast.error("Erreur lors du chargement des données");
        }
      } finally {
        setIsLoading(false);
      }
  }, [fiche?.id]);

  const fetchAttachments = useCallback(async () => {
    if (!fiche?.id) return;

    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Fetch all attachments for this fiche de travail using AllAttachment system
      const allAttachmentsResponse = await axios.get(
        `${API_BASE_URL}/all-attachments/by-audit-fiche-de-travail/${fiche.id}`,
        { headers }
      );

      if (allAttachmentsResponse.data.success) {
        const allAttachments = allAttachmentsResponse.data.data || [];
        console.log('Fetched attachments:', allAttachments);

        // Group attachments by questionID and type
        const docsMap = {};
        const refsMap = {};

        // Initialize maps for all sample numbers and questions
        sampleNumbers.forEach(sampleNumber => {
          docsMap[sampleNumber] = {};
          refsMap[sampleNumber] = {};
          questions.forEach(question => {
            // Filter attachments by questionID, type, and sampleNumber
            const questionBusinessDocs = allAttachments.filter(att =>
              att.type === 'business-document' &&
              att.questionID === question.id &&
              att.sampleNumber === sampleNumber
            );
            const questionExternalRefs = allAttachments.filter(att =>
              att.type === 'external-reference' &&
              att.questionID === question.id &&
              att.sampleNumber === sampleNumber
            );

            console.log(`Sample ${sampleNumber}, Question ${question.id}: ${questionBusinessDocs.length} docs, ${questionExternalRefs.length} refs`);

            docsMap[sampleNumber][question.id] = questionBusinessDocs;
            refsMap[sampleNumber][question.id] = questionExternalRefs;
          });
        });

        setBusinessDocuments(docsMap);
        setExternalReferences(refsMap);
      }
    } catch (error) {
      console.error("Error fetching attachments:", error);
      toast.error("Échec du chargement des pièces jointes", {
        description: "Veuillez essayer de rafraîchir la page",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  }, [fiche?.id, API_BASE_URL, questions, sampleNumbers]);

  useEffect(() => {
    if (fiche?.id) {
      fetchData();
      fetchAttachments();
    }
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fiche?.id, fiche?.questions, fetchData, fetchAttachments]);

  const handleAnswerChange = (sampleNumber, questionId, answer) => {
    setResponses(prev => {
      const newResponses = {
      ...prev,
        [sampleNumber]: {
          ...prev[sampleNumber],
          [questionId]: answer
        }
      };
      return newResponses;
    });
    debouncedSave(sampleNumber, questionId, answer);
  };

  const handleBusinessDocumentUpload = async (e, sampleNumber, questionId) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    console.log('Uploading files for question:', questionId, 'sample:', sampleNumber);

    const allowedExtensions = [
      ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
      ".txt", ".csv", ".rtf", ".odt", ".ods", ".odp",
      ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
      ".zip", ".rar", ".7z", ".tar", ".gz",
    ];

    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map((file) => file.name).join(", ");
      toast.error(`Fichiers trop volumineux : ${fileNames}`, {
        description: `Les fichiers suivants dépassent la limite de 50MB : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    const invalidFiles = files.filter((file) => {
      const extension = "." + file.name.split(".").pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });
    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map((file) => file.name).join(", ");
      toast.error(`Types de fichiers non supportés : ${fileNames}`, {
        description: `Les fichiers suivants ont des formats non supportés : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    try {
      setIsLoading(true);
      setUploadProgress(0);

      const token = localStorage.getItem('token');
      const uploadPromises = files.map(async (file, index) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'business-document');
        formData.append('auditFicheDeTravailID', fiche.id);
        formData.append('questionID', questionId);
        formData.append('sampleNumber', sampleNumber);

        const response = await axios.post(
          `${API_BASE_URL}/all-attachments/upload`,
          formData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress(Math.round(((index + progress / 100) / files.length) * 100));
            }
          }
        );

        return response.data;
      });

      const results = await Promise.all(uploadPromises);
      const successfulUploads = results.filter(result => result.success);

      if (successfulUploads.length > 0) {
        toast.success(`${successfulUploads.length} document${successfulUploads.length !== 1 ? "s" : ""} téléchargé${successfulUploads.length !== 1 ? "s" : ""} avec succès`);
        await fetchAttachments(); // Refresh the list
      }

      if (results.length !== successfulUploads.length) {
        toast.error(`${results.length - successfulUploads.length} fichier(s) n'ont pas pu être téléchargés`);
      }
    } catch (error) {
      console.error("Error uploading business documents:", error);
      if (axios.isCancel(error) || error.message.includes("timeout")) {
        toast.error("Téléchargement expiré", {
          description: "Essayez avec des fichiers plus petits ou une meilleure connexion",
          duration: 5000,
        });
      } else if (error.response?.status === 413) {
        toast.error("Fichier trop volumineux", {
          description: "Le fichier dépasse la limite de taille maximale de 50MB",
          duration: 5000,
        });
      } else if (error.response?.data?.message?.includes("file type")) {
        toast.error(`Type de fichier non supporté : ${error.response.data.message}`, {
          description: error.response.data.message,
          duration: 5000,
        });
      } else {
        const errorMsg = error.response?.data?.message || "Échec du téléchargement des documents";
        toast.error(errorMsg, { description: errorMsg, duration: 5000 });
      }
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      // Clear the specific file input
      const fileInput = document.getElementById(`file-input-${sampleNumber}-${questionId}`);
      if (fileInput) {
        fileInput.value = "";
      }
    }
  };

  const handleAddExternalReference = async () => {
    if (!newReference.url || !newReference.url.trim()) {
      toast.error("L'URL est requise");
      return;
    }

    console.log('Adding external reference for question:', newReference.questionId);

    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      const formattedUrl = newReference.url.startsWith("http://") || newReference.url.startsWith("https://")
        ? newReference.url
        : `https://${newReference.url}`;

      const referenceData = {
        url: formattedUrl,
        fileName: newReference.description || new URL(formattedUrl).hostname,
        description: newReference.description,
        type: 'external-reference',
        auditFicheDeTravailID: fiche.id,
        questionID: newReference.questionId,
        sampleNumber: newReference.sampleNumber
      };

      const response = await axios.post(
        `${API_BASE_URL}/all-attachments/reference`,
        referenceData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        toast.success("Référence ajoutée avec succès");
        setIsReferenceModalOpen(false);
        setNewReference({ url: "", description: "", questionId: null, sampleNumber: null });
        await fetchAttachments(); // Refresh the list
      } else {
        toast.error("Erreur lors de l'ajout de la référence");
      }
    } catch (error) {
      console.error("Error adding external reference:", error);
      toast.error("Échec de l'ajout de la référence", {
        description: error.response?.data?.message || "Une erreur s'est produite",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const downloadAttachment = async (id, fileName) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_BASE_URL}/all-attachments/download/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        responseType: "blob",
      });

      const contentDisposition = response.headers["content-disposition"];
      let actualFileName = fileName;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename\*?=[\'\"]?(?:UTF-8\'\'|)([a-zA-Z0-9%\-._ ]+)[\'\"]?/i);
        if (filenameMatch && filenameMatch[1]) {
          try {
            actualFileName = decodeURIComponent(filenameMatch[1]);
          } catch (_e) {
            console.warn("Could not decode filename:", filenameMatch[1]);
            actualFileName = filenameMatch[1];
          }
        }
      }

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", actualFileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading attachment:", error);
      toast.error("Téléchargement échoué", {
        description: "Impossible de télécharger le fichier. Veuillez réessayer plus tard.",
        duration: 5000,
      });
    }
  };

  const deleteBusinessDocument = async (id) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce document ?")) return;
    try {
      const token = localStorage.getItem('token');
      const response = await axios.delete(`${API_BASE_URL}/all-attachments/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.data.success) {
        toast.success("Document supprimé", {
          description: "Le document a été supprimé avec succès",
          duration: 3000,
        });
        await fetchAttachments();
      }
    } catch (error) {
      console.error("Error deleting business document:", error);
      toast.error("Échec de la suppression du document", {
        description: error.response?.data?.message || "Une erreur s'est produite",
        duration: 5000,
      });
    }
  };

  const deleteExternalReference = async (id) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cette référence ?")) return;
    try {
      const token = localStorage.getItem('token');
      const response = await axios.delete(`${API_BASE_URL}/all-attachments/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.data.success) {
        toast.success("Référence supprimée", {
          description: "La référence a été supprimée avec succès",
          duration: 3000,
        });
        await fetchAttachments();
      }
    } catch (error) {
      console.error("Error deleting external reference:", error);
      toast.error("Échec de la suppression de la référence", {
        description: error.response?.data?.message || "Une erreur s'est produite",
        duration: 5000,
      });
    }
  };

  const openExternalReference = (url) => {
    if (!url) {
      toast.error('URL invalide');
      return;
    }

    try {
      const formattedUrl = url.startsWith("http://") || url.startsWith("https://") ? url : `https://${url}`;
      window.open(formattedUrl, "_blank", "noopener,noreferrer");
    } catch (error) {
      console.error('Error opening URL:', error);
      toast.error('Impossible d\'ouvrir le lien');
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = 2;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };



  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR", { day: "2-digit", month: "2-digit", year: "numeric" });
  };

  const handleReferenceInputChange = (e) => {
    const { name, value } = e.target;
    setNewReference(prev => ({ ...prev, [name]: value }));
  };


  // Render input component based on question type
  const renderInputComponent = (question, sampleNumber) => {
    const value = responses[sampleNumber]?.[question.id] || "";
    const inputId = `sample-${sampleNumber}-question-${question.id}`;

    const docsForCell = businessDocuments[sampleNumber]?.[question.id] || [];
    const refsForCell = externalReferences[sampleNumber]?.[question.id] || [];

    switch (question.input_type) {
      case "text":
        return (
          <Input
            id={inputId}
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            placeholder="Entrez votre réponse"
            className="w-full text-xs"
          />
        );

      case "number":
        return (
          <Input
            id={inputId}
            type="number"
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            placeholder="Nombre"
            className="w-full text-xs"
          />
        );

      case "date":
        return (
          <DateInput
            id={inputId}
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            name={inputId}
            className="w-full text-xs"
          />
        );

      case "radio":
        return (
          <Select
            value={value}
            onValueChange={(val) => handleAnswerChange(sampleNumber, question.id, val)}
          >
            <SelectTrigger className="w-full text-xs h-8">
              <SelectValue placeholder="Sélectionnez" />
            </SelectTrigger>
            <SelectContent>
              {question.options?.map((option, optIndex) => (
                <SelectItem key={optIndex} value={option} className="text-xs">
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "checkbox":
      case "multi-select":
        return (
          <MultiSelect
            question={question}
            sampleNumber={sampleNumber}
            value={value}
            onAnswerChange={handleAnswerChange}
          />
        );

      case "file":
      case "document":
        return (
          <div className="flex flex-col gap-2 p-1">
            <input
              type="file"
              id={`file-input-${sampleNumber}-${question.id}`}
              onChange={(e) => handleBusinessDocumentUpload(e, sampleNumber, question.id)}
              className="hidden"
              multiple
            />
            <Button
              type="button"
              onClick={() => document.getElementById(`file-input-${sampleNumber}-${question.id}`)?.click()}
              variant="outline"
              size="sm"
              className="justify-start text-xs"
              disabled={isLoading}
            >
              {isLoading && uploadProgress > 0 ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Upload className="h-4 w-4 mr-2" />
              )}
              {isLoading && uploadProgress > 0 ? `Téléchargement (${uploadProgress}%)` : "Ajouter document"}
            </Button>
            {docsForCell.length > 0 && (
              <div className="flex flex-col gap-1">
                {docsForCell.map((doc) => (
                  <div key={doc.allAttachmentID} className="flex items-center justify-between text-xs text-gray-600 bg-gray-50 p-1 rounded">
                    <div className="flex items-center truncate">
                      <File className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate" title={doc.fileName}>{doc.fileName}</span>
                      <span className="ml-1 text-gray-500 flex-shrink-0">({formatFileSize(doc.fileSize)})</span>
                    </div>
                    <div className="flex items-center space-x-1 flex-shrink-0">
                      <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => downloadAttachment(doc.allAttachmentID, doc.fileName)} title="Télécharger">
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500" onClick={() => deleteBusinessDocument(doc.allAttachmentID)} title="Supprimer">
                        <Trash className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case "url":
      case "reference":
        return (
          <div className="flex flex-col gap-2 p-1">
            <Button
              type="button"
              onClick={() => { 
                setNewReference(prev => ({ ...prev, questionId: question.id, sampleNumber: sampleNumber }));
                setIsReferenceModalOpen(true);
              }}
              variant="outline"
              size="sm"
              className="justify-start text-xs"
            >
              <Link className="h-4 w-4 mr-2" />
              Ajouter référence
            </Button>
            {refsForCell.length > 0 && (
              <div className="flex flex-col gap-1">
                {refsForCell.map((ref) => (
                  <div key={ref.allAttachmentID} className="flex items-center justify-between text-xs text-blue-600 bg-blue-50 p-1 rounded">
                    <div className="flex items-center truncate">
                      <ExternalLink className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate" title={ref.description || ref.fileName || ref.url}>{ref.description || ref.fileName || ref.url}</span>
                    </div>
                    <div className="flex items-center space-x-1 flex-shrink-0">
                      <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => openExternalReference(ref.url || ref.filePath)} title="Ouvrir">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500" onClick={() => deleteExternalReference(ref.allAttachmentID)} title="Supprimer">
                        <Trash className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <Dialog open={isReferenceModalOpen} onOpenChange={setIsReferenceModalOpen}>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Ajouter une Référence Externe</DialogTitle>
                  <DialogDescription>
                    Ajoutez une URL et une description facultative pour cette référence.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="url">URL</Label>
                    <Input
                      id="url"
                      value={newReference.url}
                      onChange={handleReferenceInputChange}
                      name="url"
                      placeholder="https://example.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description (facultatif)</Label>
                    <Textarea
                      id="description"
                      value={newReference.description}
                      onChange={handleReferenceInputChange}
                      name="description"
                      placeholder="Description de la référence"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsReferenceModalOpen(false)}>Annuler</Button>
                  <Button onClick={handleAddExternalReference} disabled={isLoading}>
                    {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Plus className="h-4 w-4 mr-2" />}
                    Ajouter
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        );

      default:
        return (
          <Input
            id={inputId}
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            placeholder="Entrez votre réponse"
            className="w-full text-xs"
          />
        );
    }
  };

  const handleExport = () => {
    toast.info("Fonctionnalité d'export à implémenter");
  };

  const handleImport = () => {
    toast.info("Fonctionnalité d'import à implémenter");
  };

  if (!fiche) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-500"></div>
        <span className="ml-4 text-red-700">Chargement de la fiche de travail...</span>
      </div>
    );
  }

  if (sampleSize === 0) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Taille d'échantillon non définie
          </h3>
          <p className="text-gray-500">
            Veuillez définir la taille de l'échantillon dans les caractéristiques de la fiche de travail.
          </p>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucune question définie
          </h3>
          <p className="text-gray-500">
            Veuillez ajouter des questions dans les caractéristiques de la fiche de travail.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Section: Fiches de Test Header */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsHeaderOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isHeaderOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <span className="text-lg font-medium text-blue-800">Fiches de Test - {fiche.name}</span>
          </div>
        </button>
        {isHeaderOpen && (
          <div className="p-5 bg-white space-y-4">
            {/* Header Content and Main Table */}
            <div className="flex items-center justify-between w-full mb-4">
              <div className="space-y-1">
                <h2 className="text-xl font-bold text-gray-900">
                  Questionnaire - Tous les échantillons
                </h2>
                <p className="text-gray-600">
                  Taille d'échantillon: {sampleSize} | Questions: {questions.length}
                </p>
              </div>
              {/* Export/Import Buttons */}
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={handleExport}>
                  <Download className="h-4 w-4 mr-2" />
                  Exporter
                </Button>
                <Button variant="outline" size="sm" onClick={handleImport}>
                  <Upload className="h-4 w-4 mr-2" />
                  Importer
        </Button>
              </div>
      </div>
      
            {/* Main Table */}
      <div className="overflow-x-auto">
              <Table>
          <TableHeader>
                  <TableRow>
                    <TableHead className="w-20 bg-gray-50 sticky left-0 z-10">Échantillon</TableHead>
                    {questions.map((question, index) => (
                      <TableHead key={question.id} className="min-w-48 bg-gray-50">
                        <div className="text-xs">
                          <div className="font-semibold">{index + 1}. {question.question_text}</div>
                        </div>
                      </TableHead>
                    ))}
            </TableRow>
          </TableHeader>
                <TableBody>
                  {sampleNumbers.map(sampleNumber => (
                    <TableRow key={sampleNumber}>
                      <TableCell className="w-20 font-medium bg-white sticky left-0 z-10">
                        {sampleNumber}
                </TableCell>
                      {questions.map(question => (
                        <TableCell key={question.id}>
                          {renderInputComponent(question, sampleNumber)}
                  </TableCell>
                      ))}
                </TableRow>
                  ))}
          </TableBody>
        </Table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default FichesTestTab;