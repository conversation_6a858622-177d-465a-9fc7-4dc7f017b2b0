import { Route, Routes, Navigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import './styles/global.css'; // Import at the top
import { Loader2, BellRing } from "lucide-react";
import { toast, Toaster } from 'sonner';

import { checkAuth } from "./store/auth-slice";
import { fetchPaginatedRisks } from "./store/slices/riskSlice";

import { connectSocket, disconnectSocket } from './utils/socket';
import useNotifications from './hooks/useNotifications'; // Import our custom hook

import CheckAuth, { isUserAuditDirector } from "./components/common/check-auth";
import UnauthPage from "./pages/unauth-page";
import NotFound from "./pages/not-found";
import { CacheProvider, SmartAdminWelcome, SmartAdminDashboard, SmartAuditWelcome } from "./components/SmartCache";

import AuthLayout from "./components/auth/layout";
import AuthLogin from "./pages/auth/login";
import AuthRegister from "./pages/auth/register";

import AdminLayout from "./components/admin-view/layout";
import AdminDashboard from "./pages/admin-view/dashboard";
import AdminFeatures from "./pages/admin-view/features";
import AdminWelcome from "./pages/admin-view/welcome-page";

import UserLayout from "./components/user-view/layout";
import UserHome from "./pages/user-view/home"; // Import User Home page
import Incident from "./pages/admin-view/incident/incident";
import AddIncident from "./pages/admin-view/incident/add-incident";
import EditIncident from "./pages/admin-view/incident/edit-incident";
import IncidentAI from "./pages/admin-view/incident/ai/incident-ai";

import SuperAdminLayout from "./components/super-admin/layout";
import SuperAdminDashboard from "./pages/super-admin/dashboard";
import SuperAdminWelcome from "./pages/super-admin/welcome-page";
import UsersList from "./pages/super-admin/users-list";
import SuperAdminLogs from "./pages/super-admin/logs";

// Import Audit components
import AuditLayout from "./components/audit-view/layout";
import AuditWelcome from "./pages/audit-view/welcome-page";
import PlansDaudit from "./pages/audit-view/plans-daudit/plans-daudit";
import EditPlansDaudit from "./pages/audit-view/plans-daudit/edit-plans-daudit";
import CaracteristiquesTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/caracteristiques/caracteristiques.jsx";
import VueEnsembleTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/Vue-ensemble/Vue-ensemble.jsx";
import MissionsAuditsTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/missions-audits/missions-audits.jsx";
import PlanificationTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/planification/planification.jsx";
import AffectationRessourcesTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/affectation-ressources/affectation-ressources.jsx";
import { EditMissionsAuditsStandalone } from "./pages/audit-view/plans-daudit/plans-daudit-tabs/missions-audits/edit-missions-audits.jsx";
import RecommandationsTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/recommandations/recommandations.jsx";
import RapportsTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/rapports/rapports.jsx";
import FilActiviteTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/fil-activite/fil-activite.jsx";
import WorkflowsTab from "./pages/audit-view/plans-daudit/plans-daudit-tabs/workflows/workflows.jsx";

// Activites components
import EditActivites from "./pages/audit-view/plans-daudit/activites/edit-activites.jsx";
import ActivitesCaracteristiquesTab from "./pages/audit-view/plans-daudit/activites/activites-tabs/caracteristiques/caracteristiques.jsx";
import ActivitesFilActiviteTab from "./pages/audit-view/plans-daudit/activites/activites-tabs/fil-activite/fil-activite.jsx";
import ActivitesWorkflowsTab from "./pages/audit-view/plans-daudit/activites/activites-tabs/workflows/workflows.jsx";

import Profile from "./pages/common/profile";
import Risks from "./pages/admin-view/risks/risks";
import EditRisk from "./pages/admin-view/risks/edit-risk";
import RisksOverview from "./pages/admin-view/risks/risks-tabs/overview";
import RisksFeatures from "./pages/admin-view/risks/risks-tabs/features";
import RisksEvaluation from "./pages/admin-view/risks/risks-tabs/evaluation";
import RisksMitigation from "./pages/admin-view/risks/risks-tabs/mitigation";
import RisksActionPlan from "./pages/admin-view/risks/risks-tabs/action-plan";
import RisksReports from "./pages/admin-view/risks/risks-tabs/reports";
import RisksActivityFeed from "./pages/admin-view/risks/risks-tabs/activity-feed";
import RisksWorkflow from "./pages/admin-view/risks/risks-tabs/workflow";

import EntitiesManagement from "./pages/admin-view/data/entities/entities";
import EditEntity from "./pages/admin-view/data/entities/edit-entity";
import EntitiesOverview from "./pages/admin-view/data/entities/entities-tabs/overview";
import EntitiesFeatures from "./pages/admin-view/data/entities/entities-tabs/features";
import ControlsManagement from "./pages/admin-view/data/controls/controls";
import EditControl from "./pages/admin-view/data/controls/edit-control";
import ControlsOverview from "./pages/admin-view/data/controls/controls-tabs/overview";
import ControlFeatures from "./pages/admin-view/data/controls/controls-tabs/features";
import ControlEvaluation from "./pages/admin-view/data/controls/controls-tabs/evaluation";
import ControlExecution from "./pages/admin-view/data/controls/controls-tabs/execution";
import ControlDefaillances from "./pages/admin-view/data/controls/controls-tabs/defaillances";
import ControlActionPlan from "./pages/admin-view/data/controls/controls-tabs/action-plan";
import ControlFilActivite from "./pages/admin-view/data/controls/controls-tabs/fil-activite";
import CampagnesManagement from "./pages/admin-view/data/campagnes/campagnes";
import EditCampagnes from "./pages/admin-view/data/campagnes/edit-campagnes";
import VueEnsemble from "./pages/admin-view/data/campagnes/campagnes-tabs/vue-ensemble";
import Caracteristiques from "./pages/admin-view/data/campagnes/campagnes-tabs/caracteristiques";
import CampagneExecution from "./pages/admin-view/data/campagnes/campagnes-tabs/execution";
import ControlTypesManagement from "./pages/admin-view/data/control-types/control-types";
import EditControlType from "./pages/admin-view/data/control-types/edit-control-type";
import ControlTypesOverview from "./pages/admin-view/data/control-types/control-types-tabs/overview";
import ControlTypesFeatures from "./pages/admin-view/data/control-types/control-types-tabs/features";
import BusinessLinesManagement from "./pages/admin-view/data/business-lines/business-lines";
import EditBusinessLine from "./pages/admin-view/data/business-lines/edit-business-line";
import ApplicationsManagement from "./pages/admin-view/data/applications/applications";
import EditApplication from "./pages/admin-view/data/applications/edit-application";
import ApplicationOverview from "./pages/admin-view/data/applications/applications-tabs/overview";
import ApplicationFeatures from "./pages/admin-view/data/applications/applications-tabs/features";
import ActionPlansManagement from "./pages/admin-view/data/action-plans/action-plans";
import ActionsManagement from "./pages/admin-view/data/actions/actions-management";
import ActionDetails from "./pages/admin-view/data/actions/action-details";
import EditAction from "./pages/admin-view/data/actions/edit-action";
import ActionFeatures from "./pages/admin-view/data/actions/actions-tabs/features";
import ActionActivity from "./pages/admin-view/data/actions/actions-tabs/activity";
import ActionWorkflows from "./pages/admin-view/data/actions/actions-tabs/workflows";
import ActionOverview from "./pages/admin-view/data/actions/actions-tabs/overview";
import EditActionPlan from "./pages/admin-view/data/action-plans/edit-action-plan";
import ActionPlanOverview from "./pages/admin-view/data/action-plans/action-plans-tabs/overview";
import ActionPlanFeatures from "./pages/admin-view/data/action-plans/action-plans-tabs/features";
import ActionPlanActions from "./pages/admin-view/data/action-plans/action-plans-tabs/actions";
import ActionPlanProgress from "./pages/admin-view/data/action-plans/action-plans-tabs/progress";
import ActionPlanActivity from "./pages/admin-view/data/action-plans/action-plans-tabs/activity";
import ActionPlanWorkflow from "./pages/admin-view/data/action-plans/action-plans-tabs/workflow";
import BusinessProcessesManagement from "./pages/admin-view/processes/business-processes/business-processes";
import EditBusinessProcess from "./pages/admin-view/processes/business-processes/edit-business-process";
import BusinessProcessesOverview from "./pages/admin-view/processes/business-processes/business-processes-tabs/overview";
import BusinessProcessesFeatures from "./pages/admin-view/processes/business-processes/business-processes-tabs/features";
import OrganizationalProcessesManagement from "./pages/admin-view/processes/organizational-processes/organizational-processes";
import EditOrganizationalProcess from "./pages/admin-view/processes/organizational-processes/edit-organizational-process";
import OrganizationalProcessesOverview from "./pages/admin-view/processes/organizational-processes/organizational-processes-tabs/overview";
import OrganizationalProcessesFeatures from "./pages/admin-view/processes/organizational-processes/organizational-processes-tabs/features";
import OperationsManagement from "./pages/admin-view/processes/operations/operations";
import EditOperation from "./pages/admin-view/processes/operations/edit-operation";
import OperationsOverview from "./pages/admin-view/processes/operations/operations-tabs/overview";
import OperationsFeatures from "./pages/admin-view/processes/operations/operations-tabs/features";
import TreeViewPage from "./pages/admin-view/processes/tree-view";

import IncidentTypes from "./pages/admin-view/incident-types/incident-types";
import EditIncidentType from "./pages/admin-view/incident-types/edit-incident-type";
import IncidentTypesOverview from "./pages/admin-view/incident-types/incident-types-tabs/overview";
import IncidentTypesFeatures from "./pages/admin-view/incident-types/incident-types-tabs/features";
import RiskTypes from "./pages/admin-view/risk-types/risk-types";
import EditRiskType from "./pages/admin-view/risk-types/edit-risk-type";
import RiskTypesOverview from "./pages/admin-view/risk-types/risk-types-tabs/overview";
import RiskTypesFeatures from "./pages/admin-view/risk-types/risk-types-tabs/features";
import ReportsList from "./pages/admin-view/reports/reports-list";
import CreateReports from "./pages/admin-view/reports/create-reports";
import BusinessLinesOverview from "@/pages/admin-view/data/business-lines/business-lines-tabs/overview";
import BusinessLinesFeatures from "@/pages/admin-view/data/business-lines/business-lines-tabs/features";
// Rapports imports removed

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

import EditConstats from "./pages/audit-view/plans-daudit/constats/edit-constats.jsx";
import ConstatCaracteristiquesTab from "./pages/audit-view/plans-daudit/constats/constats-tabs/caracteristiques/caracteristiques.jsx";
import ConstatFilActiviteTab from "./pages/audit-view/plans-daudit/constats/constats-tabs/fil-activite/fil-activite.jsx";

import EditFichesTravail from "./pages/audit-view/plans-daudit/fiches-travail/edit-fiches-travail.jsx";
import FichesTravailCaracteristiquesTab from "./pages/audit-view/plans-daudit/fiches-travail/fiches-travail-tabs/caracteristiques/caracteristiques.jsx";
import FichesTravailFichesTestTab from "./pages/audit-view/plans-daudit/fiches-travail/fiches-travail-tabs/fiches-test/fiches-test.jsx";
import EditFichesTest from "./pages/audit-view/plans-daudit/fiches-test/edit-fiches-test.jsx";

import ConstatsTab from "./pages/audit-view/plans-daudit/activites/activites-tabs/constats/constats.jsx";
import FichesTravailTab from "./pages/audit-view/plans-daudit/activites/activites-tabs/fiches-travail/fiches-travail.jsx";

import EditRecommandation from "./pages/audit-view/plans-daudit/recommandations/edit-recommandation";
import RecommandationCaracteristiquesTab from "./pages/audit-view/plans-daudit/recommandations/recommandation-tabs/caracteristiques";
import RecommandationFilActiviteTab from "./pages/audit-view/plans-daudit/recommandations/recommandation-tabs/fil-activite";
import RecommandationPlanActionTab from "./pages/audit-view/plans-daudit/recommandations/recommandation-tabs/plan-action";
import RecommandationVueEnsembleTab from "./pages/audit-view/plans-daudit/recommandations/recommandation-tabs/vue-ensemble";
import RecommandationWorkflowTab from "./pages/audit-view/plans-daudit/recommandations/recommandation-tabs/workflow";

// Competences imports - Only for Audit Directors
import ListeCompetences from "./pages/audit-view/competences/liste-competences";
import CompetencesTeam from "./pages/audit-view/competences/competences-equipe";
import MissionCompetences from "./pages/audit-view/competences/mission-competences";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});


function App() {
  const { user, isAuthenticated, isLoading } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const location = useLocation();

  // Use our custom notifications hook instead of useState
  const {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    setNotifications
  } = useNotifications();

  // Scroll to top when location changes
  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }, [location.pathname]);

  // Top level render

  useEffect(() => {
    // Only check authentication on app startup, don't load reference data
    dispatch(checkAuth());
    // Reference data will be loaded on-demand when needed
  }, [dispatch]);

  useEffect(() => {
    if (isAuthenticated && user) {
      console.log("[App.jsx] Socket useEffect RUNNING. User ID:", user.id, "Current notifications count:", notifications.length);
      dispatch(fetchPaginatedRisks({ page: 1, pageSize: 10, filters: {} }));

      setTimeout(() => {
        console.log('[App.jsx] Attempting to connect socket after delay...');
        const socketInstance = connectSocket();

        if (socketInstance) {
          console.log('[App.jsx] Socket instance available, setting up listeners. Socket ID:', socketInstance.id);

          socketInstance.on('disconnect', (reason) => {
            console.log(`[App.jsx] Socket disconnected: ${reason}. Socket ID was: ${socketInstance.id}`);
          });

          socketInstance.on('connect_error', (error) => {
            console.error('[App.jsx] Socket connection error:', error.message, "Socket ID:", socketInstance.id);
          });

          socketInstance.on('notification', (notification) => {
            console.log('🔔 [App.jsx] Notification received via socket:', notification, "Socket ID:", socketInstance.id);
            handleNotification(notification);
          });

          socketInstance.on('welcome', (data) => {
            console.log('[App.jsx] Socket welcome event received:', data, "Socket ID:", socketInstance.id);
          });

          return () => {
            console.log('[App.jsx] Socket useEffect CLEANUP. User ID:', user.id, "Socket ID was:", socketInstance.id);
            socketInstance.off('disconnect');
            socketInstance.off('connect_error');
            socketInstance.off('notification');
            socketInstance.off('welcome');
            // Optionally disconnect if user logs out, but connectSocket handles reconnection if still logged in.
            // disconnectSocket();
          };
        } else {
          console.error('[App.jsx] Failed to get socket instance in useEffect, check authentication or connectSocket logic');
        }
      }, 500);
    } else {
      console.log("[App.jsx] Socket useEffect: User not authenticated or no user object. Ensuring socket is disconnected. IsAuth:", isAuthenticated, "User exists:", !!user);
      disconnectSocket();
    }
  // Ensure all dependencies that could meaningfully change and require re-hooking are listed.
  // addNotification and fetchNotifications from useNotifications should be stable if memoized by the hook.
  }, [dispatch, isAuthenticated, user]); // Removed addNotification, fetchNotifications if they are stable and don't cause re-runs

  const handleNotification = (notification) => {
    try {
      console.log('[App.jsx] Processing notification in handleNotification:', notification);

      if (addNotification) {
        console.log('[App.jsx] Calling addNotification from useNotifications hook.');
        addNotification(notification);

        if (toast) {
          console.log('[App.jsx] Displaying toast for notification:', notification.message);
          toast(notification.message, { // Main message as title
            description: `Type: ${notification.type} (${notification.entityName || 'N/A'})`,
            icon: <BellRing className="h-5 w-5" />,
            duration: 7000,
            // For richColors to work, the Toaster component needs `richColors` prop
            // And you'd use toast.success, toast.info, toast.error etc.
            // For a generic notification style, this is okay.
            // If you want specific colors based on type, you'd do:
            // if (notification.type === 'error') toast.error( ... )
            // else if (notification.type === 'success') toast.success( ... )
            // else toast.info( ... )
          });
        } else {
          console.warn('[App.jsx] Toast function not available in handleNotification.');
        }
      } else {
        console.error('[App.jsx] addNotification function from useNotifications hook is not available.');
      }
    } catch (error) {
      console.error('[App.jsx] Error in handleNotification:', error);
    }
  };

  if (isLoading && isAuthenticated === null) {
    console.log("[App.jsx] Initial load OR auth check pending (isLoading && isAuthenticated === null). Showing loader.");
    return (
      <div className="h-screen w-full flex items-center justify-center bg-white">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
      </div>
    );
  }

  // Determine initial redirect path for root - PRIORITIZE /admin/welcome
  let rootRedirectPath = "/auth/login"; // Default for unauthenticated
  if (isAuthenticated) {
    if (user && user.roles) {
      // Check for specific roles - ADMIN WELCOME IS NOW THE PRIMARY DEFAULT
      const isGrcAdmin = user.roles.some(role => Number(role.id) === 1);
      const isAuditDirector = user.roles.some(role => role.code === 'audit_director');
      const isAuditor = user.roles.some(role => role.code === 'auditor');

      if (isGrcAdmin) {
        rootRedirectPath = "/super-admin/welcome";
      } else if (isAuditDirector || isAuditor) {
        rootRedirectPath = "/audit/welcome";
      } else {
        // DEFAULT TO ADMIN WELCOME FOR ALL OTHER USERS
        rootRedirectPath = "/admin/welcome";
      }
    } else {
      rootRedirectPath = "/admin/welcome";
      console.log("[App.jsx] Authenticated user has no roles. Defaulting to /admin/welcome.");
    }
  }
  console.log("[App.jsx] Root redirect path determined as:", rootRedirectPath);

  return (
    <>
     <QueryClientProvider client={queryClient}>
      <CacheProvider>
        <div className="flex flex-col overflow-hidden bg-white">
          <Routes>
        {/* Root Route - Dynamic Redirect */}
        <Route path="/" element={<Navigate to={rootRedirectPath} replace />} />

          {/* Authentication Routes */}
          <Route
            path="/auth"
            element={
              <CheckAuth isAuthenticated={isAuthenticated} user={user}>
                <AuthLayout />
              </CheckAuth>
            }
          >
            <Route path="login" element={<AuthLogin />} />
            <Route path="register" element={<AuthRegister />} />
          </Route>

          {/* Admin Routes */}
          <Route
            path="/admin"
            element={
              <CheckAuth isAuthenticated={isAuthenticated} user={user}>
                <AdminLayout
                  notifications={notifications}
                  unreadCount={unreadCount}
                  markAsRead={markAsRead}
                  markAllAsRead={markAllAsRead}
                  setNotifications={setNotifications}
                />
              </CheckAuth>
            }
          >
            <Route index element={<Navigate to="/admin/welcome" replace />} />
            <Route path="dashboard" element={<SmartAdminDashboard />} />
            <Route path="welcome" element={<SmartAdminWelcome />} />
            <Route path="features" element={<AdminFeatures />} />
            <Route path="incident" element={<Incident />} />
            <Route path="incident/add" element={<AddIncident />} />
            <Route path="incident/edit/:id" element={<EditIncident />} />
            <Route path="incident/edit/:id/:tab" element={<EditIncident />} />
            <Route path="incident/ai" element={<IncidentAI />} />
            <Route path="risks" element={<Risks />} />
            <Route path="risks/edit/:id" element={<EditRisk />}>
              <Route index element={<RisksOverview />} />
              <Route path="overview" element={<RisksOverview />} />
              <Route path="features" element={<RisksFeatures />} />
              <Route path="evaluation" element={<RisksEvaluation />} />
              <Route path="mitigation" element={<RisksMitigation />} />
              <Route path="action-plan" element={<RisksActionPlan />} />
              <Route path="reports" element={<RisksReports />} />
              <Route path="workflow" element={<RisksWorkflow />} />
              <Route path="activity-feed" element={<RisksActivityFeed />} />
            </Route>
            <Route path="profile" element={<Profile />} />

            {/* Controls Routes */}
            <Route path="controls" element={<ControlsManagement />} />
            <Route path="controls/edit/:id" element={<EditControl />}>
              <Route index element={<ControlsOverview />} />
              <Route path="overview" element={<ControlsOverview />} />
              <Route path="features" element={<ControlFeatures />} />
              <Route path="evaluation" element={<ControlEvaluation />} />
              <Route path="execution" element={<ControlExecution />} />
              <Route path="defaillances" element={<ControlDefaillances />} />
              <Route path="action-plan" element={<ControlActionPlan />} />
              <Route path="fil-activite" element={<ControlFilActivite />} />
            </Route>

            {/* Campagnes Routes */}
            <Route path="campagnes" element={<CampagnesManagement />} />
            <Route path="campagnes/edit/:id" element={<EditCampagnes />}>
              <Route index element={<VueEnsemble />} />
              <Route path="vue-ensemble" element={<VueEnsemble />} />
              <Route path="caracteristiques" element={<Caracteristiques />} />
              <Route path="execution" element={<CampagneExecution />} />
            </Route>

            {/* Data Management Routes */}

            <Route path="/admin/data/entities" element={<EntitiesManagement />} />
            <Route path="/admin/data/entities/:id" element={<EditEntity />}>
              <Route index element={<EntitiesOverview />} />
              <Route path="overview" element={<EntitiesOverview />} />
              <Route path="features" element={<EntitiesFeatures />} />
            </Route>
            <Route path="/admin/data/controls" element={<Navigate to="/admin/controls" replace />} />
            <Route path="/admin/data/controls/edit/:id" element={<Navigate to={`/admin/controls/edit/${window.location.pathname.split('/').pop()}`} replace />} />
            <Route path="/admin/data/control-types" element={<ControlTypesManagement />} />
            <Route path="/admin/data/control-types/:id" element={<EditControlType />}>
              <Route index element={<ControlTypesOverview />} />
              <Route path="overview" element={<ControlTypesOverview />} />
              <Route path="features" element={<ControlTypesFeatures />} />
            </Route>
            <Route path="/admin/data/business-lines" element={<BusinessLinesManagement />} />
            <Route path="/admin/data/business-lines/:id" element={<EditBusinessLine />}>
              <Route index element={<BusinessLinesOverview />} />
              <Route path="overview" element={<BusinessLinesOverview />} />
              <Route path="features" element={<BusinessLinesFeatures />} />
            </Route>
            <Route path="/admin/data/applications" element={<ApplicationsManagement />} />
            <Route path="/admin/data/applications/:id" element={<EditApplication />}>
              <Route index element={<ApplicationOverview />} />
              <Route path="overview" element={<ApplicationOverview />} />
              <Route path="features" element={<ApplicationFeatures />} />
            </Route>
            <Route path="/admin/data/action-plans" element={<ActionPlansManagement />} />
            <Route path="/admin/data/action-plans/:id" element={<EditActionPlan />}>
              <Route index element={<ActionPlanOverview />} />
              <Route path="features" element={<ActionPlanFeatures />} />
              <Route path="actions" element={<ActionPlanActions />} />
              <Route path="progress" element={<ActionPlanProgress />} />
              <Route path="activity" element={<ActionPlanActivity />} />
              <Route path="workflow" element={<ActionPlanWorkflow />} />
            </Route>
            <Route path="/admin/data/actions/:id" element={<ActionDetails />} />
            <Route path="/admin/data/actions-management" element={<ActionsManagement />} />
            <Route path="/admin/data/actions/edit/:id" element={<EditAction />}>
              <Route index element={<ActionOverview />} />
              <Route path="overview" element={<ActionOverview />} />
              <Route path="features" element={<ActionFeatures />} />
              <Route path="activity" element={<ActionActivity />} />
              <Route path="workflows" element={<ActionWorkflows />} />
            </Route>
            <Route path="/admin/data/actions/add" element={<EditAction />}>
              <Route index element={<ActionFeatures />} />
              <Route path="features" element={<ActionFeatures />} />
              <Route path="activity" element={<ActionActivity />} />
              <Route path="workflows" element={<ActionWorkflows />} />
            </Route>
            <Route path="/admin/processes/tree-view" element={<TreeViewPage />} />
            <Route path="/admin/processes/business-processes" element={<BusinessProcessesManagement />} />
            <Route path="/admin/processes/business-processes/:id" element={<EditBusinessProcess />}>
              <Route index element={<BusinessProcessesOverview />} />
              <Route path="overview" element={<BusinessProcessesOverview />} />
              <Route path="features" element={<BusinessProcessesFeatures />} />
            </Route>
            <Route path="/admin/processes/organizational-processes" element={<OrganizationalProcessesManagement />} />
            <Route path="/admin/processes/organizational-processes/:id" element={<EditOrganizationalProcess />}>
              <Route index element={<OrganizationalProcessesOverview />} />
              <Route path="overview" element={<OrganizationalProcessesOverview />} />
              <Route path="features" element={<OrganizationalProcessesFeatures />} />
            </Route>
            <Route path="/admin/processes/operations" element={<OperationsManagement />} />
            <Route path="/admin/processes/operations/:id" element={<EditOperation />}>
              <Route index element={<OperationsOverview />} />
              <Route path="overview" element={<OperationsOverview />} />
              <Route path="features" element={<OperationsFeatures />} />
            </Route>


            <Route path="incident-types" element={<IncidentTypes />} />
            <Route path="incident-types/:id" element={<EditIncidentType />}>
              <Route index element={<IncidentTypesOverview />} />
              <Route path="overview" element={<IncidentTypesOverview />} />
              <Route path="features" element={<IncidentTypesFeatures />} />
            </Route>
            <Route path="risk-types" element={<RiskTypes />} />
            <Route path="risk-types/:id" element={<EditRiskType />}>
              <Route index element={<RiskTypesOverview />} />
              <Route path="overview" element={<RiskTypesOverview />} />
              <Route path="features" element={<RiskTypesFeatures />} />
            </Route>
            <Route path="reports" element={<ReportsList />} />
            <Route path="reports/create" element={<CreateReports />} />

            {/* Rapports Routes removed */}

            <Route path="*" element={<Navigate to="/admin/welcome" replace />} />
          </Route>

          {/* User Routes */}
          <Route
            path="/user"
            element={
              <CheckAuth isAuthenticated={isAuthenticated} user={user}>
                <UserLayout
                  notifications={notifications}
                  unreadCount={unreadCount}
                  markAsRead={markAsRead}
                  markAllAsRead={markAllAsRead}
                  setNotifications={setNotifications}
                />
              </CheckAuth>
            }
          >
            <Route index element={<Navigate to="/user/home" replace />} />
            <Route path="home" element={<UserHome />} />
            <Route path="risks" element={<Risks />} />
            <Route path="profile" element={<Profile />} />
            <Route path="*" element={<Navigate to="/user/home" replace />} />
          </Route>

          {/* Super Admin Routes */}
          <Route
            path="/super-admin"
            element={
              <CheckAuth isAuthenticated={isAuthenticated} user={user}>
                <SuperAdminLayout
                  notifications={notifications}
                  unreadCount={unreadCount}
                  markAsRead={markAsRead}
                  markAllAsRead={markAllAsRead}
                  setNotifications={setNotifications}
                />
              </CheckAuth>
            }
          >
            <Route index element={<Navigate to="/super-admin/welcome" replace />} />
            <Route path="dashboard" element={<SuperAdminDashboard />} />
            <Route path="welcome" element={<SuperAdminWelcome />} />
            <Route path="logs" element={<SuperAdminLogs />} />

            {/* User Management Routes */}
            <Route path="users" element={<UsersList />} />
            <Route path="users/add" element={<UsersList />} /> {/* Need to create this component */}
            <Route path="roles" element={<UsersList />} /> {/* Need to create this component */}

            {/* Incident Routes */}
            <Route path="incident" element={<Incident />} />
            <Route path="incident/add" element={<AddIncident />} />
            <Route path="incident/edit/:id" element={<EditIncident />} />
            <Route path="incident/edit/:id/:tab" element={<EditIncident />} />
            <Route path="incident/ai" element={<IncidentAI />} />
            <Route path="incident-types" element={<IncidentTypes />} />
            <Route path="incident-types/:id" element={<EditIncidentType />}>
              <Route index element={<IncidentTypesOverview />} />
              <Route path="overview" element={<IncidentTypesOverview />} />
              <Route path="features" element={<IncidentTypesFeatures />} />
            </Route>

            {/* Risk Routes */}
            <Route path="risks" element={<Risks />} />
            <Route path="risks/edit/:id" element={<EditRisk />}>
              <Route index element={<RisksOverview />} />
              <Route path="overview" element={<RisksOverview />} />
              <Route path="features" element={<RisksFeatures />} />
              <Route path="evaluation" element={<RisksEvaluation />} />
              <Route path="mitigation" element={<RisksMitigation />} />
              <Route path="action-plan" element={<RisksActionPlan />} />
              <Route path="reports" element={<RisksReports />} />
              <Route path="workflow" element={<RisksWorkflow />} />
              <Route path="activity-feed" element={<RisksActivityFeed />} />
            </Route>
            <Route path="risk-types" element={<RiskTypes />} />
            <Route path="risk-types/:id" element={<EditRiskType />}>
              <Route index element={<RiskTypesOverview />} />
              <Route path="overview" element={<RiskTypesOverview />} />
              <Route path="features" element={<RiskTypesFeatures />} />
            </Route>

            {/* Reports Routes */}
            <Route path="reports" element={<ReportsList />} />
            <Route path="reports/create" element={<CreateReports />} />

            {/* Rapports Routes removed */}

            {/* Process Routes */}
            <Route path="processes/tree-view" element={<TreeViewPage />} />
            <Route path="processes/business-processes" element={<BusinessProcessesManagement />} />
            <Route path="processes/business-processes/:id" element={<EditBusinessProcess />}>
              <Route index element={<BusinessProcessesOverview />} />
              <Route path="overview" element={<BusinessProcessesOverview />} />
              <Route path="features" element={<BusinessProcessesFeatures />} />
            </Route>
            <Route path="processes/organizational-processes" element={<OrganizationalProcessesManagement />} />
            <Route path="processes/organizational-processes/:id" element={<EditOrganizationalProcess />}>
              <Route index element={<OrganizationalProcessesOverview />} />
              <Route path="overview" element={<OrganizationalProcessesOverview />} />
              <Route path="features" element={<OrganizationalProcessesFeatures />} />
            </Route>
            <Route path="processes/operations" element={<OperationsManagement />} />
            <Route path="processes/operations/:id" element={<EditOperation />}>
              <Route index element={<OperationsOverview />} />
              <Route path="overview" element={<OperationsOverview />} />
              <Route path="features" element={<OperationsFeatures />} />
            </Route>

            {/* Data Routes */}
            <Route path="data/entities" element={<EntitiesManagement />} />
            <Route path="data/entities/:id" element={<EditEntity />}>
              <Route index element={<EntitiesOverview />} />
              <Route path="overview" element={<EntitiesOverview />} />
              <Route path="features" element={<EntitiesFeatures />} />
            </Route>
            <Route path="data/controls" element={<ControlsManagement />} />
            <Route path="campagnes" element={<CampagnesManagement />} />
            <Route path="campagnes/edit/:id" element={<EditCampagnes />}>
              <Route index element={<VueEnsemble />} />
              <Route path="vue-ensemble" element={<VueEnsemble />} />
              <Route path="caracteristiques" element={<Caracteristiques />} />
              <Route path="execution" element={<CampagneExecution />} />
            </Route>
            <Route path="data/control-types" element={<ControlTypesManagement />} />
            <Route path="data/control-types/:id" element={<EditControlType />}>
              <Route index element={<ControlTypesOverview />} />
              <Route path="overview" element={<ControlTypesOverview />} />
              <Route path="features" element={<ControlTypesFeatures />} />
            </Route>
            <Route path="data/business-lines" element={<BusinessLinesManagement />} />
            <Route path="data/applications" element={<ApplicationsManagement />} />
            <Route path="data/applications/:id" element={<EditApplication />}>
              <Route index element={<ApplicationOverview />} />
              <Route path="overview" element={<ApplicationOverview />} />
              <Route path="features" element={<ApplicationFeatures />} />
            </Route>
            <Route path="data/action-plans" element={<ActionPlansManagement />} />
            <Route path="data/action-plans/:id" element={<EditActionPlan />}>
              <Route index element={<ActionPlanOverview />} />
              <Route path="features" element={<ActionPlanFeatures />} />
              <Route path="actions" element={<ActionPlanActions />} />
              <Route path="progress" element={<ActionPlanProgress />} />
              <Route path="activity" element={<ActionPlanActivity />} />
              <Route path="workflow" element={<ActionPlanWorkflow />} />
            </Route>
            <Route path="data/actions/:id" element={<ActionDetails />} />

            <Route path="profile" element={<Profile />} />
            <Route path="settings" element={<AdminFeatures />} /> {/* Using AdminFeatures as a placeholder */}
            <Route path="*" element={<Navigate to="/super-admin/welcome" replace />} />
          </Route>

        {/* Audit Routes */}
        <Route
          path="/audit"
          element={
            <CheckAuth isAuthenticated={isAuthenticated} user={user}>
              <AuditLayout
                notifications={notifications}
                unreadCount={unreadCount}
                markAsRead={markAsRead}
                markAllAsRead={markAllAsRead}
                setNotifications={setNotifications}
              />
            </CheckAuth>
          }
        >
          <Route index element={<Navigate to="/audit/welcome" replace />} />
          <Route path="welcome" element={<SmartAuditWelcome />} />
          <Route path="plans-daudit" element={<PlansDaudit />} />
          <Route path="plans-daudit/:id" element={<EditPlansDaudit />}>
            <Route index element={<VueEnsembleTab />} />
            <Route path="caracteristiques" element={<CaracteristiquesTab />} />
            <Route path="missions-audits" element={<MissionsAuditsTab />} />
            <Route path="planification" element={<PlanificationTab />} />
            <Route path="affectation-ressources" element={<AffectationRessourcesTab />} />
            <Route path="recommandations" element={<RecommandationsTab />} />
            <Route path="rapports" element={<RapportsTab />} />
            <Route path="fil-activite" element={<FilActiviteTab />} />
            <Route path="workflow" element={<WorkflowsTab />} />
            <Route path="workflows" element={<WorkflowsTab />} />
          </Route>
          {/* Standalone route for plan-specific missions */}
          <Route path="plans-daudit/:planId/missions-audits/:missionAuditId" element={<EditMissionsAuditsStandalone />} />

          {/* Nested routes for activities under plan-specific missions */}
          <Route path="plans-daudit/:planId/missions-audits/:missionAuditId/activites/:activiteId" element={<EditActivites />}>
            <Route index element={<ActivitesCaracteristiquesTab />} />
            <Route path="constats" element={<ConstatsTab />} />
            <Route path="fiches-travail" element={<FichesTravailTab />} />
            <Route path="fil-activite" element={<ActivitesFilActiviteTab />} />
            <Route path="workflows" element={<ActivitesWorkflowsTab />} />
          </Route>
          {/* Standalone route for constats under activite */}
          <Route path="plans-daudit/:planId/missions-audits/:missionAuditId/activites/:activiteId/constats/:constatId" element={<EditConstats />}>
            <Route index element={<ConstatCaracteristiquesTab />} />
            <Route path="fil-activite" element={<ConstatFilActiviteTab />} />
          </Route>

          {/* Standalone route for recommendations under constats */}
          <Route path="plans-daudit/:planId/missions-audits/:missionAuditId/activites/:activiteId/constats/:constatId/recommandations/:recommandationId" element={<EditRecommandation />}>
            <Route index element={<RecommandationCaracteristiquesTab />} />
            <Route path="fil-activite" element={<RecommandationFilActiviteTab />} />
            <Route path="plan-action" element={<RecommandationPlanActionTab />} />
            <Route path="vue-ensemble" element={<RecommandationVueEnsembleTab />} />
            <Route path="workflow" element={<RecommandationWorkflowTab />} />
          </Route>

          {/* Standalone route for fiches de travail under plan-specific missions */}
          <Route path="plans-daudit/:planId/missions-audits/:missionAuditId/activites/:activiteId/fiches-travail/:ficheId" element={<EditFichesTravail />}>
            <Route index element={<FichesTravailCaracteristiquesTab />} />
            <Route path="fiches-test" element={<FichesTravailFichesTestTab />} />
          </Route>

          {/* Standalone route for fiches de travail under mission-only context (no planId) */}
          <Route path="missions-audits/:missionAuditId/activites/:activiteId/fiches-travail/:ficheId" element={<EditFichesTravail />}>
            <Route index element={<FichesTravailCaracteristiquesTab />} />
            <Route path="fiches-test" element={<FichesTravailFichesTestTab />} />
          </Route>

          {/* Standalone route for fiches de test */}
          <Route path="plans-daudit/fiches-test/:id" element={<EditFichesTest />} />

          {/* Standalone route for missions d'audit - keeping it for backward compatibility */}
          <Route path="missions-audits/:missionAuditId" element={<EditMissionsAuditsStandalone />} />

          {/* Nested routes for activities under standalone missions (for backward compatibility) */}
          <Route path="missions-audits/:missionAuditId/activites/:activiteId" element={<EditActivites />}>
            <Route index element={<ActivitesCaracteristiquesTab />} />
            <Route path="constats" element={<ConstatsTab />} />
            <Route path="fiches-travail" element={<FichesTravailTab />} />
            <Route path="fil-activite" element={<ActivitesFilActiviteTab />} />
            <Route path="workflows" element={<ActivitesWorkflowsTab />} />
          </Route>
          {/* Standalone route for constats under activite (without planId) */}
          <Route path="missions-audits/:missionAuditId/activites/:activiteId/constats/:constatId" element={<EditConstats />}>
            <Route index element={<ConstatCaracteristiquesTab />} />
            <Route path="fil-activite" element={<ConstatFilActiviteTab />} />
          </Route>
          {/* Standalone route for recommendations under constats (without planId) */}
          <Route path="missions-audits/:missionAuditId/activites/:activiteId/constats/:constatId/recommandations/:recommandationId" element={<EditRecommandation />}>
            <Route index element={<RecommandationCaracteristiquesTab />} />
            <Route path="fil-activite" element={<RecommandationFilActiviteTab />} />
            <Route path="plan-action" element={<RecommandationPlanActionTab />} />
            <Route path="vue-ensemble" element={<RecommandationVueEnsembleTab />} />
            <Route path="workflow" element={<RecommandationWorkflowTab />} />
          </Route>
          <Route path="profile" element={<Profile />} />
          {/* Risks Routes */}
          <Route path="risks" element={<Risks />} />
          <Route path="risks/edit/:id" element={<EditRisk />}>
            <Route index element={<RisksOverview />} />
            <Route path="overview" element={<RisksOverview />} />
            <Route path="features" element={<RisksFeatures />} />
            <Route path="evaluation" element={<RisksEvaluation />} />
            <Route path="mitigation" element={<RisksMitigation />} />
            <Route path="action-plan" element={<RisksActionPlan />} />
            <Route path="reports" element={<RisksReports />} />
            <Route path="workflow" element={<RisksWorkflow />} />
            <Route path="activity-feed" element={<RisksActivityFeed />} />
          </Route>
          {/* Risk Types */}
          <Route path="risk-types" element={<RiskTypes />} />
          <Route path="risk-types/:id" element={<EditRiskType />}>
            <Route index element={<RiskTypesOverview />} />
            <Route path="overview" element={<RiskTypesOverview />} />
            <Route path="features" element={<RiskTypesFeatures />} />
          </Route>
          {/* Incidents Routes */}
          <Route path="incident" element={<Incident />} />
          <Route path="incident/add" element={<AddIncident />} />
          <Route path="incident/edit/:id" element={<EditIncident />} />
          <Route path="incident/edit/:id/:tab" element={<EditIncident />} />
          <Route path="incident/ai" element={<IncidentAI />} />
          {/* Incident Types */}
          <Route path="incident-types" element={<IncidentTypes />} />
          <Route path="incident-types/:id" element={<EditIncidentType />}>
            <Route index element={<IncidentTypesOverview />} />
            <Route path="overview" element={<IncidentTypesOverview />} />
            <Route path="features" element={<IncidentTypesFeatures />} />
          </Route>
          {/* Controls Routes */}
          <Route path="controls" element={<ControlsManagement />} />
          <Route path="controls/edit/:id" element={<EditControl />}>
            <Route index element={<ControlsOverview />} />
            <Route path="overview" element={<ControlsOverview />} />
            <Route path="features" element={<ControlFeatures />} />
            <Route path="evaluation" element={<ControlEvaluation />} />
            <Route path="execution" element={<ControlExecution />} />
            <Route path="defaillances" element={<ControlDefaillances />} />
            <Route path="action-plan" element={<ControlActionPlan />} />
            <Route path="fil-activite" element={<ControlFilActivite />} />
          </Route>
          {/* Processes Routes */}
          <Route path="processes/tree-view" element={<TreeViewPage />} />
          <Route path="processes/business-processes" element={<BusinessProcessesManagement />} />
          <Route path="processes/business-processes/:id" element={<EditBusinessProcess />}>
            <Route index element={<BusinessProcessesOverview />} />
            <Route path="overview" element={<BusinessProcessesOverview />} />
            <Route path="features" element={<BusinessProcessesFeatures />} />
          </Route>
          <Route path="processes/organizational-processes" element={<OrganizationalProcessesManagement />} />
          <Route path="processes/organizational-processes/:id" element={<EditOrganizationalProcess />}>
            <Route index element={<OrganizationalProcessesOverview />} />
            <Route path="overview" element={<OrganizationalProcessesOverview />} />
            <Route path="features" element={<OrganizationalProcessesFeatures />} />
          </Route>
          <Route path="processes/operations" element={<OperationsManagement />} />
          <Route path="processes/operations/:id" element={<EditOperation />}>
            <Route index element={<OperationsOverview />} />
            <Route path="overview" element={<OperationsOverview />} />
            <Route path="features" element={<OperationsFeatures />} />
          </Route>
          {/* Data Routes */}
          <Route path="data/entities" element={<EntitiesManagement />} />
          <Route path="data/entities/:id" element={<EditEntity />}>
            <Route index element={<EntitiesOverview />} />
            <Route path="overview" element={<EntitiesOverview />} />
            <Route path="features" element={<EntitiesFeatures />} />
          </Route>
          <Route path="data/controls" element={<ControlsManagement />} />
          <Route path="campagnes" element={<CampagnesManagement />} />
          <Route path="campagnes/edit/:id" element={<EditCampagnes />}>
            <Route index element={<VueEnsemble />} />
            <Route path="vue-ensemble" element={<VueEnsemble />} />
            <Route path="caracteristiques" element={<Caracteristiques />} />
            <Route path="execution" element={<CampagneExecution />} />
          </Route>
          <Route path="data/control-types" element={<ControlTypesManagement />} />
          <Route path="data/control-types/:id" element={<EditControlType />}>
            <Route index element={<ControlTypesOverview />} />
            <Route path="overview" element={<ControlTypesOverview />} />
            <Route path="features" element={<ControlTypesFeatures />} />
          </Route>
          <Route path="data/business-lines" element={<BusinessLinesManagement />} />
          <Route path="data/applications" element={<ApplicationsManagement />} />
          <Route path="data/applications/:id" element={<EditApplication />}>
            <Route index element={<ApplicationOverview />} />
            <Route path="overview" element={<ApplicationOverview />} />
            <Route path="features" element={<ApplicationFeatures />} />
          </Route>
          <Route path="data/action-plans" element={<ActionPlansManagement />} />
          <Route path="data/action-plans/:id" element={<EditActionPlan />}>
            <Route index element={<ActionPlanOverview />} />
            <Route path="features" element={<ActionPlanFeatures />} />
            <Route path="actions" element={<ActionPlanActions />} />
            <Route path="progress" element={<ActionPlanProgress />} />
            <Route path="activity" element={<ActionPlanActivity />} />
            <Route path="workflow" element={<ActionPlanWorkflow />} />
          </Route>
          <Route path="data/actions/:id" element={<ActionDetails />} />
          <Route path="profile" element={<Profile />} />
          <Route path="settings" element={<AdminFeatures />} /> {/* Using AdminFeatures as a placeholder */}

          {/* Competences Routes - Only for Audit Directors */}
          <Route
            path="liste-competences"
            element={
              isUserAuditDirector(user) ? (
                <ListeCompetences />
              ) : (
                <Navigate to="/audit/welcome" replace />
              )
            }
          />
          <Route
            path="competences-equipe"
            element={
              isUserAuditDirector(user) ? (
                <CompetencesTeam />
              ) : (
                <Navigate to="/audit/welcome" replace />
              )
            }
          />
          <Route
            path="mission-competences"
            element={
              isUserAuditDirector(user) ? (
                <MissionCompetences />
              ) : (
                <Navigate to="/audit/welcome" replace />
              )
            }
          />

          <Route path="*" element={<Navigate to="/audit/welcome" replace />} />
        </Route>

          {/* Fallback Routes */}
          <Route path="*" element={<NotFound />} />
          <Route path="/unauth-page" element={<UnauthPage />} />
        </Routes>
        </div>
      </CacheProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
      </>
  );
}

export default App;






