const db = require('../../models');
const AuditMission = db.AuditMission;
const User = db.User;
const AuditPlan = db.AuditPlan;
const EquipeIntervenante = db.EquipeIntervenante;
const AuditMissionSkill = db.AuditMissionSkill;
const AuditActivity = db.AuditActivity;
const AuditScope = db.AuditScope;
const AuditConstat = db.AuditConstat;
const AuditRecommendation = db.AuditRecommendation;
const FicheDeTravail = db.FicheDeTravail;
const { v4: uuidv4 } = require('uuid');
const { sendEmailDirect } = require('../email-controller');

// Get all audit missions - OPTIMIZED VERSION
const getAllAuditMissions = async (req, res) => {
  try {
    // Add pagination support
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    // Check if this is for statistics only (welcome page)
    const statsOnly = req.query.statsOnly === 'true';

    if (statsOnly) {
      // For statistics, only return counts grouped by status
      const statsQuery = `
        SELECT
          etat,
          COUNT(*) as count
        FROM "AuditMissions"
        GROUP BY etat
      `;

      const stats = await db.sequelize.query(statsQuery, {
        type: db.sequelize.QueryTypes.SELECT
      });

      return res.status(200).json({
        success: true,
        data: stats,
        isStats: true
      });
    }

    // Optimized query with specific attributes and ordering
    const auditMissions = await AuditMission.findAndCountAll({
      attributes: [
        'id', 'name', 'categorie', 'code', 'etat',
        'principalAudite', 'objectif', 'avancement', 'planifieInitialement',
        'evaluation', 'datedebut', 'datefin', 'pointfort', 'pointfaible',
        'auditplanID', 'createdAt', 'updatedAt'
      ],
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name', 'status'],
          required: false // LEFT JOIN for better performance
        },
        {
          model: EquipeIntervenante,
          as: 'equipeIntervenantes',
          attributes: ['id', 'userId', 'chefdemission'],
          where: { chefdemission: 'oui' },
          required: false,
          include: [{ model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }]
        }
      ],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: auditMissions.rows,
      pagination: {
        total: auditMissions.count,
        page,
        limit,
        totalPages: Math.ceil(auditMissions.count / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching audit missions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit missions',
      error: error.message
    });
  }
};

// Get audit missions by plan ID
const getAuditMissionsByPlanId = async (req, res) => {
  try {
    const { planId } = req.params;

    // Optimized query with indexed where clause and minimal includes
    const auditMissions = await AuditMission.findAll({
      where: { auditplanID: planId },
      attributes: [
        'id', 'name', 'categorie', 'code', 'etat',
        'principalAudite', 'objectif', 'avancement', 'planifieInitialement',
        'evaluation', 'datedebut', 'datefin', 'pointfort', 'pointfaible',
        'auditplanID', 'createdAt', 'updatedAt'
      ],
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name', 'status'],
          required: false // LEFT JOIN for better performance
        },
        {
          model: EquipeIntervenante,
          as: 'equipeIntervenantes',
          attributes: [
            'id', 'userId', 'chefdemission', 'auditActivityId', 'auditConstatId', 'auditRecommendationId', 'createdAt', 'updatedAt'
          ],
          required: false,
          include: [{ model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }]
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: auditMissions
    });
  } catch (error) {
    console.error('Error fetching audit missions by plan ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit missions',
      error: error.message
    });
  }
};

// Create a new audit mission
const createAuditMission = async (req, res) => {
  try {
    const {
      name,
      categorie,
      code,
      etat,
      principalAudite,
      objectif,
      avancement,
      planifieInitialement,
      evaluation,
      datedebut,
      datefin,
      pointfort,
      pointfaible,
      auditplanID
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name is required for audit mission'
      });
    }
    
    if (!auditplanID) {
      return res.status(400).json({
        success: false,
        message: 'Audit plan ID is required'
      });
    }
    
    // Check if audit plan exists
    const auditPlan = await AuditPlan.findByPk(auditplanID);
    if (!auditPlan) {
      return res.status(404).json({
        success: false,
        message: 'Audit plan not found'
      });
    }
    
    // Create the audit mission
    const auditMission = await AuditMission.create({
      id: `AM_${uuidv4().substring(0, 8)}`, // Generate a unique ID with prefix
      name,
      categorie,
      code,
      etat: etat || 'Planned',
      principalAudite,
      objectif,
      avancement,
      planifieInitialement: planifieInitialement || false,
      evaluation,
      datedebut,
      datefin,
      pointfort,
      pointfaible,
      auditplanID
    });

    return res.status(201).json({
      success: true,
      message: 'Audit mission created successfully',
      data: auditMission
    });
  } catch (error) {
    console.error('Error creating audit mission:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create audit mission',
      error: error.message
    });
  }
};

// Get audit mission by ID
const getAuditMissionById = async (req, res) => {
  try {
    const { id } = req.params;
    const mission = await AuditMission.findByPk(id, {
      include: [
        {
          model: db.AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name']
        },
        {
          model: db.EquipeIntervenante,
          as: 'equipeIntervenantes',
          include: [
            { model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }
          ]
        }
      ]
    });
    if (!mission) {
      return res.status(404).json({ success: false, message: 'Audit mission not found' });
    }
    return res.status(200).json({ success: true, data: mission });
  } catch (error) {
    console.error('Error fetching audit mission:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch audit mission', error: error.message });
  }
};

// Update audit mission
const updateAuditMission = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      categorie,
      code,
      etat,
      principalAudite,
      objectif,
      avancement,
      planifieInitialement,
      evaluation,
      datedebut,
      datefin,
      pointfort,
      pointfaible,
      auditplanID
    } = req.body;
    
    const auditMission = await AuditMission.findByPk(id);
    
    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }
    
    // If auditplanID is being changed, check if the new plan exists
    if (auditplanID && auditplanID !== auditMission.auditplanID) {
      const auditPlan = await AuditPlan.findByPk(auditplanID);
      if (!auditPlan) {
        return res.status(404).json({
          success: false,
          message: 'New audit plan not found'
        });
      }
    }
    
    // Update the audit mission
    await auditMission.update({
      name: name !== undefined ? name : auditMission.name,
      categorie: categorie !== undefined ? categorie : auditMission.categorie,
      code: code !== undefined ? code : auditMission.code,
      etat: etat !== undefined ? etat : auditMission.etat,
      principalAudite: principalAudite !== undefined ? principalAudite : auditMission.principalAudite,
      objectif: objectif !== undefined ? objectif : auditMission.objectif,
      avancement: avancement !== undefined ? avancement : auditMission.avancement,
      planifieInitialement: planifieInitialement !== undefined ? planifieInitialement : auditMission.planifieInitialement,
      evaluation: evaluation !== undefined ? evaluation : auditMission.evaluation,
      datedebut: datedebut !== undefined ? datedebut : auditMission.datedebut,
      datefin: datefin !== undefined ? datefin : auditMission.datefin,
      pointfort: pointfort !== undefined ? pointfort : auditMission.pointfort,
      pointfaible: pointfaible !== undefined ? pointfaible : auditMission.pointfaible,
      auditplanID: auditplanID !== undefined ? auditplanID : auditMission.auditplanID
    });
    
    // Fetch the updated audit mission with related data
    const updatedAuditMission = await AuditMission.findByPk(id, {
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name']
        },
        {
          model: EquipeIntervenante,
          as: 'equipeIntervenantes',
          attributes: ['id', 'userId', 'chefdemission'],
          where: { chefdemission: 'oui' },
          required: false,
          include: [{ model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }]
        }
      ]
    });
    
    return res.status(200).json({
      success: true,
      message: 'Audit mission updated successfully',
      data: updatedAuditMission
    });
  } catch (error) {
    console.error('Error updating audit mission:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update audit mission',
      error: error.message
    });
  }
};

// Delete audit mission
const deleteAuditMission = async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`[AUDIT_MISSION] Starting deletion process for mission: ${id}`);

    const auditMission = await AuditMission.findByPk(id);

    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }

    // Start a transaction to ensure all deletions succeed or fail together
    const transaction = await db.sequelize.transaction();

    try {
      let deletionSummary = {
        mission: auditMission.name,
        skills: 0,
        scopes: 0,
        activities: 0
      };

      // Step 1: Delete all related AuditMissionSkills first
      try {
        const deletedSkills = await AuditMissionSkill.destroy({
          where: { missionId: id },
          transaction
        });
        deletionSummary.skills = deletedSkills;
        console.log(`[AUDIT_MISSION] Deleted ${deletedSkills} mission skills`);
      } catch (skillError) {
        console.log(`[AUDIT_MISSION] No mission skills to delete or error: ${skillError.message}`);
      }

      // Step 2: Delete all related AuditScopes
      try {
        const deletedScopes = await AuditScope.destroy({
          where: { auditMissionID: id },
          transaction
        });
        deletionSummary.scopes = deletedScopes;
        console.log(`[AUDIT_MISSION] Deleted ${deletedScopes} audit scopes`);
      } catch (scopeError) {
        console.log(`[AUDIT_MISSION] No audit scopes to delete or error: ${scopeError.message}`);
      }

      // Step 3: Delete all related AuditActivities
      try {
        const deletedActivities = await AuditActivity.destroy({
          where: { auditMissionID: id },
          transaction
        });
        deletionSummary.activities = deletedActivities;
        console.log(`[AUDIT_MISSION] Deleted ${deletedActivities} audit activities`);
      } catch (activityError) {
        console.log(`[AUDIT_MISSION] No audit activities to delete or error: ${activityError.message}`);
      }

      // Step 4: Delete the audit mission itself
      await auditMission.destroy({ transaction });
      console.log(`[AUDIT_MISSION] Deleted audit mission: ${auditMission.name}`);

      // Commit the transaction
      await transaction.commit();

      return res.status(200).json({
        success: true,
        message: `Audit mission "${auditMission.name}" and all related data deleted successfully`,
        deletedData: deletionSummary
      });

    } catch (transactionError) {
      // Rollback the transaction on error
      await transaction.rollback();
      throw transactionError;
    }

  } catch (error) {
    console.error('Error deleting audit mission:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to delete audit mission';
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      errorMessage = 'Cannot delete audit mission: it has related data that must be removed first';
    }

    return res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message,
      details: error.name === 'SequelizeForeignKeyConstraintError' ?
        'This mission has related activities or other data. Please contact support if this error persists.' :
        undefined
    });
  }
};

// Workflow transition function
const updateMissionWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const { transition, workflowMessage } = req.body;

    // Validate required fields
    if (!transition) {
      return res.status(400).json({
        success: false,
        message: 'Transition is required for workflow transition'
      });
    }

    // Find the audit mission
    const auditMission = await AuditMission.findByPk(id);

    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }

    // Define workflow steps and status mapping
    const workflowSteps = [
      'A valider',
      'A publier',
      'Programme de travail a soumettre',
      'Programme de travail a valider',
      'Rapport final a soumettre',
      'Rapport final a valider',
      'Rapport final a envoyer',
      'A fermer',
      'Fermé'
    ];

    // Validate transition value
    if (!workflowSteps.includes(transition)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid transition value',
        validValues: workflowSteps
      });
    }

    // Map transition to etat
    let newEtat;
    switch (transition) {
      case 'A valider':
      case 'A publier':
        newEtat = 'En préparation';
        break;
      case 'Programme de travail a soumettre':
        newEtat = 'Publié';
        break;
      case 'Programme de travail a valider':
      case 'Rapport final a soumettre':
      case 'Rapport final a valider':
        newEtat = 'En cours';
        break;
      case 'Rapport final a envoyer':
      case 'A fermer':
        newEtat = 'Terminée';
        break;
      case 'Fermé':
        newEtat = 'Fermée';
        break;
      default:
        newEtat = auditMission.etat; // Keep current state if no mapping
    }

    // Update the audit mission
    await auditMission.update({
      transition: transition,
      etat: newEtat
    });

    // Fetch the updated audit mission with related data
    const updatedAuditMission = await AuditMission.findByPk(id, {
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name']
        },
        {
          model: EquipeIntervenante,
          as: 'equipeIntervenantes',
          attributes: ['id', 'userId', 'chefdemission'],
          where: { chefdemission: 'oui' },
          required: false,
          include: [{ model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }]
        }
      ]
    });

    return res.status(200).json({
      success: true,
      message: 'Mission workflow updated successfully',
      data: updatedAuditMission,
      transition: {
        from: auditMission.transition,
        to: transition,
        etatUpdated: newEtat,
        message: workflowMessage
      }
    });
  } catch (error) {
    console.error('Error updating mission workflow:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update mission workflow',
      error: error.message
    });
  }
};

// Get workflow history for a mission (mock implementation for now)
const getMissionWorkflowHistory = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the audit mission
    const auditMission = await AuditMission.findByPk(id, {
      include: [
        {
          model: AuditPlan,
          as: 'auditPlan',
          attributes: ['id', 'name']
        },
        {
          model: EquipeIntervenante,
          as: 'equipeIntervenantes',
          attributes: ['id', 'userId', 'chefdemission'],
          where: { chefdemission: 'oui' },
          required: false,
          include: [{ model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }]
        }
      ]
    });

    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }

    // Mock workflow history based on current state
    const workflowHistory = [];

    // Always have creation event
    workflowHistory.push({
      timestamp: auditMission.createdAt,
      transition: 'A valider',
      etat: 'En préparation',
      user: 'Chef de mission',
      transitionType: 'Create',
      message: 'Mission d\'audit créée'
    });

    // Add progression events based on current transition
    const currentTransition = auditMission.transition;
    const workflowSteps = [
      'A valider',
      'A publier',
      'Programme de travail a soumettre',
      'Programme de travail a valider',
      'Rapport final a soumettre',
      'Rapport final a valider',
      'Rapport final a envoyer',
      'A fermer',
      'Fermé'
    ];

    const currentIndex = workflowSteps.indexOf(currentTransition);

    // Add events for completed steps
    for (let i = 1; i <= currentIndex; i++) {
      const step = workflowSteps[i];
      let etat;
      switch (step) {
        case 'A publier':
          etat = 'En préparation';
          break;
        case 'Programme de travail a soumettre':
          etat = 'Publié';
          break;
        case 'Programme de travail a valider':
        case 'Rapport final a soumettre':
        case 'Rapport final a valider':
          etat = 'En cours';
          break;
        case 'Rapport final a envoyer':
        case 'A fermer':
          etat = 'Terminée';
          break;
        case 'Fermé':
          etat = 'Fermée';
          break;
        default:
          etat = 'En préparation';
      }

      workflowHistory.push({
        timestamp: auditMission.updatedAt,
        transition: step,
        etat: etat,
        user: 'Chef de mission',
        transitionType: 'Advance',
        message: `Progression vers: ${step}`
      });
    }

    // Sort by timestamp (most recent first)
    workflowHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return res.status(200).json({
      success: true,
      data: {
        mission: auditMission,
        history: workflowHistory
      }
    });
  } catch (error) {
    console.error('Error fetching mission workflow history:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch mission workflow history',
      error: error.message
    });
  }
};

module.exports = {
  getAllAuditMissions,
  getAuditMissionsByPlanId,
  createAuditMission,
  getAuditMissionById,
  updateAuditMission,
  deleteAuditMission,
  updateMissionWorkflow,
  getMissionWorkflowHistory
};