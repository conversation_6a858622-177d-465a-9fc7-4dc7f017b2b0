const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getMissionCharges,
  getMissionChargesSummary,
  createMissionCharge,
  updateMissionCharge,
  deleteMissionCharge
} = require('../../controllers/audit/audit-mission-charges-controller');

console.log('AuditMissionCharges routes loaded');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all charges for a specific mission
// Query params: ?type=internal|external|all (optional)
router.get('/mission/:missionId', 
  authorizeRoles(['audit_director', 'auditor']), 
  getMissionCharges
);

// Get charges summary for a mission
router.get('/mission/:missionId/summary', 
  authorizeRoles(['audit_director', 'auditor']), 
  getMissionChargesSummary
);

// Create new charge for a mission
router.post('/mission/:missionId', 
  authorizeRoles(['audit_director', 'auditor']), 
  createMissionCharge
);

// Update a specific charge
router.put('/mission/:missionId/charge/:chargeId', 
  authorizeRoles(['audit_director', 'auditor']), 
  updateMissionCharge
);

// Delete a specific charge
router.delete('/mission/:missionId/charge/:chargeId', 
  authorizeRoles(['audit_director', 'auditor']), 
  deleteMissionCharge
);

module.exports = router;
