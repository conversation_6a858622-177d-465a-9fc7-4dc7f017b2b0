'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditMissionRapportSupport = sequelize.define('AuditMissionRapportSupport', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    logo: {
      type: DataTypes.TEXT, // Store base64 encoded image or file path
      allowNull: true
    },
    signatureElectrique: {
      type: DataTypes.TEXT, // Store base64 encoded image or file path
      allowNull: true
    },
    destinataire: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    auditMissionId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditMissions',
        key: 'id'
      }
    }
  }, {
    tableName: 'AuditMissionRapportSupports',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['auditMissionId']
      }
    ]
  });

  AuditMissionRapportSupport.associate = function(models) {
    // AuditMissionRapportSupport belongs to AuditMission
    AuditMissionRapportSupport.belongsTo(models.AuditMission, {
      foreignKey: 'auditMissionId',
      as: 'auditMission'
    });
  };

  return AuditMissionRapportSupport;
};
// node scripts/sync-rapport-support-table.js