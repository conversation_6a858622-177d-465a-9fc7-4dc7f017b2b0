const db = require('../../models');
const AuditMissionCharges = db.AuditMissionCharges;
const AuditMission = db.AuditMission;
const { v4: uuidv4 } = require('uuid');

// Get all charges for a specific mission
const getMissionCharges = async (req, res) => {
  try {
    const { missionId } = req.params;
    const { type } = req.query; // 'internal', 'external', or 'all'

    // Verify mission exists
    const mission = await AuditMission.findByPk(missionId);
    if (!mission) {
      return res.status(404).json({
        success: false,
        message: 'Mission d\'audit non trouvée'
      });
    }

    // Build where clause based on type filter
    let whereClause = { missionId };
    if (type === 'internal') {
      whereClause.isExternal = false;
    } else if (type === 'external') {
      whereClause.isExternal = true;
    }

    const charges = await AuditMissionCharges.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']]
    });

    // Calculate totals
    const internalCharges = charges.filter(c => !c.isExternal);
    const externalCharges = charges.filter(c => c.isExternal);
    
    const internalTotal = internalCharges.reduce((sum, charge) => sum + charge.totalCost, 0);
    const externalTotal = externalCharges.reduce((sum, charge) => sum + charge.totalCost, 0);
    const grandTotal = internalTotal + externalTotal;

    return res.status(200).json({
      success: true,
      data: {
        charges,
        summary: {
          internalTotal,
          externalTotal,
          grandTotal,
          internalCount: internalCharges.length,
          externalCount: externalCharges.length,
          totalCount: charges.length
        }
      }
    });
  } catch (error) {
    console.error('Error fetching mission charges:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des charges de la mission',
      error: error.message
    });
  }
};

// Get charges summary for a mission
const getMissionChargesSummary = async (req, res) => {
  try {
    const { missionId } = req.params;

    // Verify mission exists
    const mission = await AuditMission.findByPk(missionId);
    if (!mission) {
      return res.status(404).json({
        success: false,
        message: 'Mission d\'audit non trouvée'
      });
    }

    const charges = await AuditMissionCharges.findAll({
      where: { missionId }
    });

    // Calculate totals and breakdown
    const internalCharges = charges.filter(c => !c.isExternal);
    const externalCharges = charges.filter(c => c.isExternal);
    
    const internalTotal = internalCharges.reduce((sum, charge) => sum + charge.totalCost, 0);
    const externalTotal = externalCharges.reduce((sum, charge) => sum + charge.totalCost, 0);
    const grandTotal = internalTotal + externalTotal;

    // Detailed breakdown
    const breakdown = {
      internal: internalCharges.map(charge => ({
        id: charge.id,
        ressourceName: charge.ressourceName,
        costPerUnit: charge.costPerUnit,
        duration: charge.duration,
        totalCost: charge.totalCost,
        currency: charge.currency
      })),
      external: externalCharges.map(charge => ({
        id: charge.id,
        prestataireName: charge.prestataireName,
        serviceDescription: charge.serviceDescription,
        totalCost: charge.totalCost,
        currency: charge.currency
      }))
    };

    return res.status(200).json({
      success: true,
      data: {
        summary: {
          internalTotal,
          externalTotal,
          grandTotal,
          internalCount: internalCharges.length,
          externalCount: externalCharges.length,
          totalCount: charges.length
        },
        breakdown
      }
    });
  } catch (error) {
    console.error('Error fetching mission charges summary:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du résumé des charges',
      error: error.message
    });
  }
};

// Create a new charge
const createMissionCharge = async (req, res) => {
  try {
    const { missionId } = req.params;
    const {
      ressourceName,
      prestataireName,
      serviceDescription,
      costPerUnit,
      duration,
      isExternal,
      currency
    } = req.body;

    // Verify mission exists
    const mission = await AuditMission.findByPk(missionId);
    if (!mission) {
      return res.status(404).json({
        success: false,
        message: 'Mission d\'audit non trouvée'
      });
    }

    // Validate required fields based on charge type
    if (!isExternal) {
      // Internal charge validation
      if (!ressourceName || !costPerUnit || !duration) {
        return res.status(400).json({
          success: false,
          message: 'Pour les charges internes, les champs ressourceName, costPerUnit et duration sont requis'
        });
      }
    } else {
      // External charge validation
      if (!prestataireName || !serviceDescription || !costPerUnit) {
        return res.status(400).json({
          success: false,
          message: 'Pour les charges externes, les champs prestataireName, serviceDescription et costPerUnit sont requis'
        });
      }
    }

    // Create the charge
    const charge = await AuditMissionCharges.create({
      id: `AMC_${uuidv4().substring(0, 8)}`,
      missionId,
      ressourceName: isExternal ? null : ressourceName,
      prestataireName: isExternal ? prestataireName : null,
      serviceDescription: isExternal ? serviceDescription : null,
      costPerUnit: parseFloat(costPerUnit),
      duration: isExternal ? null : parseFloat(duration),
      isExternal: Boolean(isExternal),
      currency: currency || 'EUR'
    });

    return res.status(201).json({
      success: true,
      message: 'Charge créée avec succès',
      data: charge
    });
  } catch (error) {
    console.error('Error creating mission charge:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la charge',
      error: error.message
    });
  }
};

// Update a charge
const updateMissionCharge = async (req, res) => {
  try {
    const { missionId, chargeId } = req.params;
    const {
      ressourceName,
      prestataireName,
      serviceDescription,
      costPerUnit,
      duration,
      isExternal,
      currency
    } = req.body;

    // Find the charge
    const charge = await AuditMissionCharges.findOne({
      where: { id: chargeId, missionId }
    });

    if (!charge) {
      return res.status(404).json({
        success: false,
        message: 'Charge non trouvée'
      });
    }

    // Validate required fields based on charge type
    if (!isExternal) {
      // Internal charge validation
      if (!ressourceName || !costPerUnit || !duration) {
        return res.status(400).json({
          success: false,
          message: 'Pour les charges internes, les champs ressourceName, costPerUnit et duration sont requis'
        });
      }
    } else {
      // External charge validation
      if (!prestataireName || !serviceDescription || !costPerUnit) {
        return res.status(400).json({
          success: false,
          message: 'Pour les charges externes, les champs prestataireName, serviceDescription et costPerUnit sont requis'
        });
      }
    }

    // Update the charge
    await charge.update({
      ressourceName: isExternal ? null : ressourceName,
      prestataireName: isExternal ? prestataireName : null,
      serviceDescription: isExternal ? serviceDescription : null,
      costPerUnit: parseFloat(costPerUnit),
      duration: isExternal ? null : parseFloat(duration),
      isExternal: Boolean(isExternal),
      currency: currency || charge.currency
    });

    return res.status(200).json({
      success: true,
      message: 'Charge mise à jour avec succès',
      data: charge
    });
  } catch (error) {
    console.error('Error updating mission charge:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la charge',
      error: error.message
    });
  }
};

// Delete a charge
const deleteMissionCharge = async (req, res) => {
  try {
    const { missionId, chargeId } = req.params;

    // Find the charge
    const charge = await AuditMissionCharges.findOne({
      where: { id: chargeId, missionId }
    });

    if (!charge) {
      return res.status(404).json({
        success: false,
        message: 'Charge non trouvée'
      });
    }

    // Delete the charge
    await charge.destroy();

    return res.status(200).json({
      success: true,
      message: 'Charge supprimée avec succès'
    });
  } catch (error) {
    console.error('Error deleting mission charge:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la charge',
      error: error.message
    });
  }
};

module.exports = {
  getMissionCharges,
  getMissionChargesSummary,
  createMissionCharge,
  updateMissionCharge,
  deleteMissionCharge
};
