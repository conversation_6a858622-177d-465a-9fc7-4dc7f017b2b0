const { AuditMission, AuditMissionSkill, AuditSkill } = require('../../models');

// Get all missions with their required skills
const getAllMissionsWithSkills = async (req, res) => {
  try {
    console.log('[AUDIT_MISSION_SKILLS] Fetching all missions with skills');

    const missions = await AuditMission.findAll({
      include: [
        {
          model: AuditMissionSkill,
          as: 'requiredSkills',
          include: [
            {
              model: AuditSkill,
              as: 'skill',
              where: { isActive: true }
            }
          ]
        }
      ],
      order: [['name', 'ASC']]
    });

    console.log(`[AUDIT_MISSION_SKILLS] Found ${missions.length} missions`);

    res.status(200).json({
      success: true,
      data: missions
    });
  } catch (error) {
    console.error('[AUDIT_MISSION_SKILLS] Error fetching missions with skills:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du chargement des missions'
    });
  }
};

// Get mission skills by mission ID
const getMissionSkills = async (req, res) => {
  try {
    const { missionId } = req.params;
    console.log(`[AUDIT_MISSION_SKILLS] Fetching skills for mission: ${missionId}`);

    const mission = await AuditMission.findByPk(missionId, {
      include: [
        {
          model: AuditMissionSkill,
          as: 'requiredSkills',
          include: [
            {
              model: AuditSkill,
              as: 'skill',
              where: { isActive: true }
            }
          ]
        }
      ]
    });

    if (!mission) {
      return res.status(404).json({
        success: false,
        message: 'Mission non trouvée'
      });
    }

    console.log(`[AUDIT_MISSION_SKILLS] Found ${mission.requiredSkills.length} skills for mission`);

    res.status(200).json({
      success: true,
      data: mission
    });
  } catch (error) {
    console.error('[AUDIT_MISSION_SKILLS] Error fetching mission skills:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du chargement des compétences de la mission'
    });
  }
};

// Add skill to mission
const addSkillToMission = async (req, res) => {
  try {
    const { missionId } = req.params;
    const { skillId, requiredLevel, importance } = req.body;

    console.log(`[AUDIT_MISSION_SKILLS] Adding skill ${skillId} to mission ${missionId}`);

    // Validate input
    if (!skillId || !requiredLevel || !importance) {
      return res.status(400).json({
        success: false,
        message: 'Tous les champs sont requis'
      });
    }

    if (requiredLevel < 1 || requiredLevel > 5) {
      return res.status(400).json({
        success: false,
        message: 'Le niveau requis doit être entre 1 et 5'
      });
    }

    if (!['CRITICAL', 'IMPORTANT', 'NICE_TO_HAVE'].includes(importance)) {
      return res.status(400).json({
        success: false,
        message: 'Importance invalide'
      });
    }

    // Check if mission exists
    const mission = await AuditMission.findByPk(missionId);
    if (!mission) {
      return res.status(404).json({
        success: false,
        message: 'Mission non trouvée'
      });
    }

    // Check if skill exists
    const skill = await AuditSkill.findOne({
      where: { id: skillId, isActive: true }
    });
    if (!skill) {
      return res.status(404).json({
        success: false,
        message: 'Compétence non trouvée'
      });
    }

    // Check if skill is already assigned to mission
    const existingAssignment = await AuditMissionSkill.findOne({
      where: { missionId, skillId }
    });

    if (existingAssignment) {
      return res.status(409).json({
        success: false,
        message: 'Cette compétence est déjà assignée à cette mission'
      });
    }

    // Create the assignment
    const missionSkill = await AuditMissionSkill.create({
      missionId,
      skillId,
      requiredLevel,
      importance
    });

    // Fetch the created assignment with skill details
    const createdAssignment = await AuditMissionSkill.findByPk(missionSkill.id, {
      include: [
        {
          model: AuditSkill,
          as: 'skill'
        }
      ]
    });

    console.log(`[AUDIT_MISSION_SKILLS] Successfully added skill to mission`);

    res.status(201).json({
      success: true,
      message: `Compétence "${skill.name}" ajoutée à la mission`,
      data: createdAssignment
    });
  } catch (error) {
    console.error('[AUDIT_MISSION_SKILLS] Error adding skill to mission:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout de la compétence à la mission'
    });
  }
};

// Update mission skill requirement
const updateMissionSkill = async (req, res) => {
  try {
    const { missionId, skillId } = req.params;
    const { requiredLevel, importance } = req.body;

    console.log(`[AUDIT_MISSION_SKILLS] Updating skill ${skillId} for mission ${missionId}`);

    // Validate input
    if (requiredLevel && (requiredLevel < 1 || requiredLevel > 5)) {
      return res.status(400).json({
        success: false,
        message: 'Le niveau requis doit être entre 1 et 5'
      });
    }

    if (importance && !['CRITICAL', 'IMPORTANT', 'NICE_TO_HAVE'].includes(importance)) {
      return res.status(400).json({
        success: false,
        message: 'Importance invalide'
      });
    }

    // Find the mission skill assignment
    const missionSkill = await AuditMissionSkill.findOne({
      where: { missionId, skillId },
      include: [
        {
          model: AuditSkill,
          as: 'skill'
        }
      ]
    });

    if (!missionSkill) {
      return res.status(404).json({
        success: false,
        message: 'Assignation de compétence non trouvée'
      });
    }

    // Update the assignment
    const updateData = {};
    if (requiredLevel !== undefined) updateData.requiredLevel = requiredLevel;
    if (importance !== undefined) updateData.importance = importance;

    await missionSkill.update(updateData);

    console.log(`[AUDIT_MISSION_SKILLS] Successfully updated mission skill`);

    res.status(200).json({
      success: true,
      message: `Exigence pour "${missionSkill.skill.name}" mise à jour`,
      data: missionSkill
    });
  } catch (error) {
    console.error('[AUDIT_MISSION_SKILLS] Error updating mission skill:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de l\'exigence de compétence'
    });
  }
};

// Remove skill from mission
const removeSkillFromMission = async (req, res) => {
  try {
    const { missionId, skillId } = req.params;

    console.log(`[AUDIT_MISSION_SKILLS] Removing skill ${skillId} from mission ${missionId}`);

    // Find the mission skill assignment
    const missionSkill = await AuditMissionSkill.findOne({
      where: { missionId, skillId },
      include: [
        {
          model: AuditSkill,
          as: 'skill'
        }
      ]
    });

    if (!missionSkill) {
      return res.status(404).json({
        success: false,
        message: 'Assignation de compétence non trouvée'
      });
    }

    const skillName = missionSkill.skill.name;

    // Delete the assignment
    await missionSkill.destroy();

    console.log(`[AUDIT_MISSION_SKILLS] Successfully removed skill from mission`);

    res.status(200).json({
      success: true,
      message: `Compétence "${skillName}" retirée de la mission`
    });
  } catch (error) {
    console.error('[AUDIT_MISSION_SKILLS] Error removing skill from mission:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du retrait de la compétence de la mission'
    });
  }
};

module.exports = {
  getAllMissionsWithSkills,
  getMissionSkills,
  addSkillToMission,
  updateMissionSkill,
  removeSkillFromMission
};
