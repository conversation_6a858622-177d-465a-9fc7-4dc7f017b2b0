'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditMissionCharges = sequelize.define('AuditMissionCharges', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    missionId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditMissions',
        key: 'id'
      }
    },
    ressourceName: {
      type: DataTypes.STRING,
      allowNull: true, // Only for internal charges
      validate: {
        isRequiredForInternal(value) {
          if (!this.isExternal && !value) {
            throw new Error('Ressource name is required for internal charges');
          }
        }
      }
    },
    prestataireName: {
      type: DataTypes.STRING,
      allowNull: true, // Only for external charges
      validate: {
        isRequiredForExternal(value) {
          if (this.isExternal && !value) {
            throw new Error('Prestataire name is required for external charges');
          }
        }
      }
    },
    serviceDescription: {
      type: DataTypes.TEXT,
      allowNull: true, // Only for external charges
      validate: {
        isRequiredForExternal(value) {
          if (this.isExternal && !value) {
            throw new Error('Service description is required for external charges');
          }
        }
      }
    },
    costPerUnit: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0,
        isDecimal: true
      }
    },
    duration: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true, // Only for internal charges
      validate: {
        min: 0,
        isDecimal: true,
        isRequiredForInternal(value) {
          if (!this.isExternal && (value === null || value === undefined)) {
            throw new Error('Duration is required for internal charges');
          }
        },
        isNullForExternal(value) {
          if (this.isExternal && value !== null && value !== undefined) {
            throw new Error('Duration should not be set for external charges');
          }
        }
      }
    },
    isExternal: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'EUR',
      validate: {
        isIn: [['EUR', 'USD', 'GBP', 'CHF', 'CAD', 'XOF']]
      }
    }
  }, {
    tableName: 'AuditMissionCharges',
    timestamps: true,
    // Auto-create table with indexes
    sync: { force: false },
    indexes: [
      {
        fields: ['missionId']
      },
      {
        fields: ['isExternal']
      },
      {
        fields: ['missionId', 'isExternal']
      }
    ],
    // Add virtual field for calculated total cost
    getterMethods: {
      totalCost() {
        if (this.isExternal) {
          // For external charges, costPerUnit is the total cost
          return parseFloat(this.costPerUnit);
        } else {
          // For internal charges, calculate: costPerUnit * duration
          return parseFloat(this.costPerUnit) * parseFloat(this.duration || 0);
        }
      }
    }
  });

  AuditMissionCharges.associate = function(models) {
    // AuditMissionCharges belongs to AuditMission
    AuditMissionCharges.belongsTo(models.AuditMission, {
      foreignKey: 'missionId',
      as: 'auditMission'
    });
  };

  // Optional: Add sample data seeding function
  AuditMissionCharges.seedSampleData = async function(models) {
    try {
      // Check if we already have data
      const existingCount = await AuditMissionCharges.count();
      if (existingCount > 0) {
        console.log('[Seed] AuditMissionCharges already has data, skipping seeding');
        return;
      }

      // Get some existing missions to seed data for
      const missions = await models.AuditMission.findAll({ limit: 2 });
      if (missions.length === 0) {
        console.log('[Seed] No audit missions found, skipping AuditMissionCharges seeding');
        return;
      }

      const { v4: uuidv4 } = require('uuid');
      const sampleCharges = [];

      missions.forEach((mission) => {
        // Add sample internal charges
        sampleCharges.push(
          {
            id: `AMC_${uuidv4().substring(0, 8)}`,
            missionId: mission.id,
            ressourceName: 'Auditeur Senior',
            prestataireName: null,
            serviceDescription: null,
            costPerUnit: 85.00,
            duration: 40.00,
            isExternal: false,
            currency: 'EUR'
          },
          {
            id: `AMC_${uuidv4().substring(0, 8)}`,
            missionId: mission.id,
            ressourceName: 'Auditeur Junior',
            prestataireName: null,
            serviceDescription: null,
            costPerUnit: 55.00,
            duration: 60.00,
            isExternal: false,
            currency: 'EUR'
          }
        );

        // Add sample external charges
        sampleCharges.push(
          {
            id: `AMC_${uuidv4().substring(0, 8)}`,
            missionId: mission.id,
            ressourceName: null,
            prestataireName: 'Cabinet Expert Comptable',
            serviceDescription: 'Analyse approfondie des états financiers',
            costPerUnit: 2500.00,
            duration: null,
            isExternal: true,
            currency: 'EUR'
          }
        );
      });

      await AuditMissionCharges.bulkCreate(sampleCharges);
      console.log(`[Seed] Created ${sampleCharges.length} sample audit mission charges`);
    } catch (error) {
      console.error('[Seed] Error seeding AuditMissionCharges:', error);
    }
  };

  return AuditMissionCharges;
};
