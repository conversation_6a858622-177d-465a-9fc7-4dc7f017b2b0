'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class AuditMissionSkill extends Model {
    static associate(models) {
      // Association with AuditMission
      AuditMissionSkill.belongsTo(models.AuditMission, {
        foreignKey: 'missionId',
        as: 'mission'
      });

      // Association with AuditSkill
      AuditMissionSkill.belongsTo(models.AuditSkill, {
        foreignKey: 'skillId',
        as: 'skill'
      });
    }
  }

  AuditMissionSkill.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    missionId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditMissions',
        key: 'id'
      }
    },
    skillId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditSkills',
        key: 'id'
      }
    },
    requiredLevel: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 5
      }
    },
    importance: {
      type: DataTypes.ENUM('CRITICAL', 'IMPORTANT', 'NICE_TO_HAVE'),
      allowNull: false,
      defaultValue: 'IMPORTANT'
    }
  }, {
    sequelize,
    modelName: 'AuditMissionSkill',
    tableName: 'audit_mission_skills',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['missionId', 'skillId'],
        name: 'unique_mission_skill'
      }
    ]
  });

  return AuditMissionSkill;
};
