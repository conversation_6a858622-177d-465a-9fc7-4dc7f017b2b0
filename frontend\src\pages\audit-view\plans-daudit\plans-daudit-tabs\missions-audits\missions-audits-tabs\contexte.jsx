import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from 'react';
import { debounce } from "lodash";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  ClipboardList,
  Save,
  FileSymlink,
  ChevronDown,
  ChevronUp,
  FileText,
  Calendar,
  <PERSON>,
  Clock,
  <PERSON><PERSON><PERSON>,
  CheckSquare,
  AlertTriangle,
  CheckCircle,
  CircleAlert,
  User,
  Search,
  Map,
  Link,
  Edit,
  Trash2,
  Plus,
  Unlink as BrokenChain
} from "lucide-react";
import { toast } from "sonner";
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import {
  fetchAuditMissionById,
  updateAuditMission,
  clearCurrentMission,
  clearError
} from '@/store/slices/audit/auditMissionsSlice';
import { Checkbox } from "@/components/ui/checkbox";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import { getAuditScopeByMissionId, updateAuditScopeRelationships } from '@/services/audit-scope-service';

// Import asset icons
import entityIcon from '@/assets/entity.png';
import riskIcon from '@/assets/risk.png';
import controlIcon from '@/assets/control.png';
import orgProcIcon from '@/assets/orgproc.png';
import bpsIcon from '@/assets/BPS.png';

// Add Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error in component:', error);
    console.error('Error info:', errorInfo);
    toast.error('Une erreur est survenue dans le composant');
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-500 rounded bg-red-50">
          <h2 className="text-red-700 font-bold">Une erreur est survenue</h2>
          <pre className="text-sm text-red-600 mt-2">
            {this.state.error?.toString()}
          </pre>
          <Button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="mt-2"
          >
            Réessayer
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

function CaracteristiquesTab() {
  const { missionAuditId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const abortControllerRef = useRef(null);
  const isMountedRef = useRef(true);

  // Redux state for mission
  const { currentItem: missionAudit, loading: missionLoading, error: missionError, lastFetched } = useSelector(
    (state) => state.auditMissions
  );

  // Local state for users
  const [users, setUsers] = useState([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [codeValidation, setCodeValidation] = useState({ isValid: true, message: '', isChecking: false });
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Section states
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isPerimetreOpen, setIsPerimetreOpen] = useState(true);
  const [isJalonsOpen, setIsJalonsOpen] = useState(true);

  // Perimetre states
  const [perimetreItems, setPerimetreItems] = useState([]);
  const [isLoadingPerimetre, setIsLoadingPerimetre] = useState(false);
  const [unlinkingItemId, setUnlinkingItemId] = useState(null);
  const [isPerimetreDialogOpen, setIsPerimetreDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [editingPerimetreItem, setEditingPerimetreItem] = useState(null);
  const [newPerimetreItem, setNewPerimetreItem] = useState({ nom: "", type: "Processus" });
  const [entities, setEntities] = useState([]);
  const [businessProcesses, setBusinessProcesses] = useState([]);
  const [organizationalProcesses, setOrganizationalProcesses] = useState([]);
  const [risks, setRisks] = useState([]);
  const [controls, setControls] = useState([]);
  const [selectedItems, setSelectedItems] = useState({
    entities: [],
    risks: [],
    businessProcesses: [],
    organizationalProcesses: [],
    controls: []
  });
  const [isLoadingLinkData, setIsLoadingLinkData] = useState(false);
  const [linkSearch, setLinkSearch] = useState("");
  const [activeTab, setActiveTab] = useState("entities");
  const [perimetreSearch, setPerimetreSearch] = useState("");
  
  // Initialize characteristics with safe defaults
  const [characteristics, setCharacteristics] = useState({
    title: '',
    code: '',
    category: '',
    status: 'En cours',
    auditPlan: '',
    includedInInitialPlan: false,
    objectives: '',
    progression: 0,
    evaluation: 'Bon niveau',
    recommendations: 0
  });

  // Add at the top of the component
  const [initialLoad, setInitialLoad] = useState(true);

  // Helper function to get the appropriate icon for périmètre item type
  const getPerimetreIcon = (type, referenceType) => {
    const iconStyle = "h-4 w-4 mr-2 flex-shrink-0";

    // Use referenceType for more accurate icon selection
    switch (referenceType) {
      case 'entity':
        return <img src={entityIcon} alt="Entity" className={iconStyle} />;
      case 'risk':
        return <img src={riskIcon} alt="Risk" className={iconStyle} />;
      case 'control':
        return <img src={controlIcon} alt="Control" className={iconStyle} />;
      case 'organizationalProcess':
        return <img src={orgProcIcon} alt="Organizational Process" className={iconStyle} />;
      case 'businessProcess':
        return <img src={bpsIcon} alt="Business Process" className={iconStyle} />;
      default:
        // Fallback to type-based selection
        switch (type) {
          case 'Entité':
            return <img src={entityIcon} alt="Entity" className={iconStyle} />;
          case 'Risque':
            return <img src={riskIcon} alt="Risk" className={iconStyle} />;
          case 'Contrôle':
            return <img src={controlIcon} alt="Control" className={iconStyle} />;
          case 'Processus organisationnel':
            return <img src={orgProcIcon} alt="Organizational Process" className={iconStyle} />;
          case 'Processus métier':
            return <img src={bpsIcon} alt="Business Process" className={iconStyle} />;
          case 'Processus':
            return <img src={bpsIcon} alt="Process" className={iconStyle} />;
          default:
            return <Map className={`${iconStyle} text-gray-500`} />;
        }
    }
  };

  const getTypeBadge = (type) => {
    switch (type) {
      case 'Processus': return <Badge className="bg-blue-100 text-blue-800">Processus</Badge>;
      case 'Système': return <Badge className="bg-green-100 text-green-800">Système</Badge>;
      case 'Organisation': return <Badge className="bg-purple-100 text-purple-800">Organisation</Badge>;
      case 'Risque': return <Badge className="bg-amber-100 text-amber-800">Risque</Badge>;
      case 'Contrôle': return <Badge className="bg-emerald-100 text-emerald-800">Contrôle</Badge>;
      case 'Processus métier': return <Badge className="bg-indigo-100 text-indigo-800">Processus métier</Badge>;
      case 'Processus organisationnel': return <Badge className="bg-violet-100 text-violet-800">Processus org.</Badge>;
      default: return <Badge variant="outline">{type}</Badge>;
    }
  };

  const removeDuplicates = (array, idKey) => {
    const seen = new Set();
    return array.map((item, index) => {
      const id = item[idKey] !== undefined ? String(item[idKey]) : `temp-${index}`;
      if (seen.has(id)) {
        console.warn(`Duplicate ${idKey} found: ${id}`);
        return null;
      }
      seen.add(id);
      return { ...item, [idKey]: id };
    }).filter(item => item !== null);
  };

  // Fetch users (local, not Redux)
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoadingUsers(true);
        const response = await axios.get(`${getApiBaseUrl()}/users`);
        if (response.data.success && Array.isArray(response.data.data)) {
          setUsers(response.data.data);
        } else {
          setUsers([]);
          toast.error('Données des utilisateurs non valides');
        }
      } catch (error) {
        setUsers([]);
        toast.error('Erreur lors du chargement des utilisateurs');
      } finally {
        setIsLoadingUsers(false);
      }
    };
    fetchUsers();
  }, []);

  // Fetch mission data with cancellation
  useEffect(() => {
    if (abortControllerRef.current) abortControllerRef.current.abort();
    abortControllerRef.current = new AbortController();
    if (missionAuditId && (!missionAudit || missionAudit.id !== missionAuditId || !lastFetched)) {
      dispatch(fetchAuditMissionById({ id: missionAuditId, signal: abortControllerRef.current.signal }))
        .unwrap()
        .catch((error) => {
          if (error && error.name !== 'CanceledError') {
            toast.error(error || 'Erreur lors du chargement de la mission');
            navigate(-1);
          }
        });
    }
    return () => {
      if (abortControllerRef.current) abortControllerRef.current.abort();
      if (!location.pathname.includes('/missions-audits/')) {
        dispatch(clearCurrentMission());
      }
    };
  }, [missionAuditId, dispatch, navigate, missionAudit, lastFetched, location.pathname]);

  // Show error toast if Redux error
  useEffect(() => {
    if (missionError) {
      toast.error(missionError);
      dispatch(clearError());
    }
  }, [missionError, dispatch]);

  // Update characteristics when mission data changes
  useEffect(() => {
    if (missionAudit && !isLoadingUsers) {
      setCharacteristics({
        title: missionAudit.name || '',
        code: missionAudit.code || '',
        category: missionAudit.categorie || '',
        status: missionAudit.etat || 'En cours',
        auditPlan: missionAudit.auditPlan?.name || '',
        includedInInitialPlan: missionAudit.planifieInitialement || false,
        objectives: missionAudit.objectif || '',
        progression: parseInt(missionAudit.avancement) || 0,
        evaluation: missionAudit.evaluation || 'Bon niveau',
        recommendations: 0
      });
    }
  }, [missionAudit, users, isLoadingUsers]);

  useEffect(() => {
    if (missionAudit && !isLoadingUsers && initialLoad) {
      setInitialLoad(false);
    }
  }, [missionAudit, isLoadingUsers, initialLoad]);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Fetch audit scope when mission changes
  useEffect(() => {
    if (missionAudit?.id) {
      fetchAuditScope();
    }
  }, [missionAudit?.id]);

  // Initialize selected items when link dialog opens
  useEffect(() => {
    if (isLinkDialogOpen && perimetreItems) {
      const initialSelected = {
        entities: perimetreItems.filter(item => item.referenceType === "entity").map(item => String(item.referenceId)),
        risks: perimetreItems.filter(item => item.referenceType === "risk").map(item => String(item.referenceId)),
        businessProcesses: perimetreItems.filter(item => item.referenceType === "businessProcess").map(item => String(item.referenceId)),
        organizationalProcesses: perimetreItems.filter(item => item.referenceType === "organizationalProcess").map(item => String(item.referenceId)),
        controls: perimetreItems.filter(item => item.referenceType === "control").map(item => String(item.referenceId)),
      };
      setSelectedItems(initialSelected);
      fetchLinkData();
    }
  }, [isLinkDialogOpen, perimetreItems]);



  // Fetch audit scope data
  const fetchAuditScope = async () => {
    if (!missionAudit?.id) return;

    setIsLoadingPerimetre(true);
    try {
      const response = await getAuditScopeByMissionId(missionAudit.id);
      if (response.success && response.data && response.data.length > 0) {
        // Get the first (and should be only) audit scope for this mission
        const auditScope = response.data[0];

        // Transform the scope data into the format expected by the component
        const scopeItems = [];

        // Add entities from many-to-many relationship
        if (auditScope.entities && Array.isArray(auditScope.entities)) {
          auditScope.entities.forEach(entity => {
            scopeItems.push({
              id: `entity-${entity.entityID}`,
              nom: entity.name,
              type: "Entité",
              referenceId: entity.entityID,
              referenceType: "entity"
            });
          });
        }

        // Add risks from many-to-many relationship
        if (auditScope.risks && Array.isArray(auditScope.risks)) {
          auditScope.risks.forEach(risk => {
            scopeItems.push({
              id: `risk-${risk.riskID}`,
              nom: risk.name,
              type: "Risque",
              referenceId: risk.riskID,
              referenceType: "risk"
            });
          });
        }

        // Add organizational processes from many-to-many relationship
        if (auditScope.processes && Array.isArray(auditScope.processes)) {
          auditScope.processes.forEach(process => {
            scopeItems.push({
              id: `organizationalProcess-${process.organizationalProcessID}`,
              nom: process.name,
              type: "Processus organisationnel",
              referenceId: process.organizationalProcessID,
              referenceType: "organizationalProcess"
            });
          });
        }

        // Add controls from many-to-many relationship
        if (auditScope.controls && Array.isArray(auditScope.controls)) {
          auditScope.controls.forEach(control => {
            scopeItems.push({
              id: `control-${control.controlID}`,
              nom: control.name,
              type: "Contrôle",
              referenceId: control.controlID,
              referenceType: "control"
            });
          });
        }

        // Add business processes from many-to-many relationship
        if (auditScope.businessProcesses && Array.isArray(auditScope.businessProcesses)) {
          auditScope.businessProcesses.forEach(process => {
            scopeItems.push({
              id: `businessProcess-${process.businessProcessID}`,
              nom: process.name,
              type: "Processus métier",
              referenceId: process.businessProcessID,
              referenceType: "businessProcess"
            });
          });
        }

        setPerimetreItems(scopeItems);
        console.log('Fetched audit scope items:', scopeItems);
      } else {
        // No scope found, start with empty array
        setPerimetreItems([]);
        console.log('No audit scope found for mission:', missionAudit.id);
      }
    } catch (error) {
      console.error('Error fetching audit scope:', error);
      toast.error('Erreur lors du chargement du périmètre');
      setPerimetreItems([]);
    } finally {
      setIsLoadingPerimetre(false);
    }
  };

  // Fetch link data for the dialog
  const fetchLinkData = async () => {
    setIsLoadingLinkData(true);
    try {
      const API_BASE_URL = getApiBaseUrl();
      const [entitiesResponse, risksResponse, businessProcessesResponse, organizationalProcessesResponse, controlsResponse] = await Promise.all([
        axios.get(`${API_BASE_URL}/entities`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [] } })),
        axios.get(`${API_BASE_URL}/risk`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [] } })),
        axios.get(`${API_BASE_URL}/businessprocesses`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [] } })),
        axios.get(`${API_BASE_URL}/organizationalprocesses`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [] } })),
        axios.get(`${API_BASE_URL}/controls`, { withCredentials: true }).catch(() => ({ data: { success: true, data: [] } }))
      ]);

      const uniqueEntities = removeDuplicates(entitiesResponse.data.data || [], 'entityID');
      const uniqueRisks = removeDuplicates(risksResponse.data.data || [], 'riskID');
      const uniqueBusinessProcesses = removeDuplicates(businessProcessesResponse.data.data || [], 'businessProcessID');
      const uniqueOrganizationalProcesses = removeDuplicates(organizationalProcessesResponse.data.data || [], 'organizationalProcessID');
      const uniqueControls = removeDuplicates(controlsResponse.data.data || [], 'controlID');

      if (entitiesResponse.data.success) setEntities(uniqueEntities);
      if (risksResponse.data.success) setRisks(uniqueRisks);
      if (businessProcessesResponse.data.success) setBusinessProcesses(uniqueBusinessProcesses);
      if (organizationalProcessesResponse.data.success) setOrganizationalProcesses(uniqueOrganizationalProcesses);
      if (controlsResponse.data.success) setControls(uniqueControls);
    } catch (error) {
      console.error("Error fetching link data:", error);
      toast.error("Erreur lors du chargement des données de référence");
    } finally {
      setIsLoadingLinkData(false);
    }
  };

  // Handle unlinking perimetre item
  const handleUnlinkPerimetreItem = async (item) => {
    if (!missionAudit?.id) return;

    setUnlinkingItemId(item.id);
    try {
      // Get current relationships
      const currentRelationships = {
        entities: perimetreItems
          .filter(i => i.referenceType === 'entity' && i.id !== item.id)
          .map(i => i.referenceId),
        risks: perimetreItems
          .filter(i => i.referenceType === 'risk' && i.id !== item.id)
          .map(i => i.referenceId),
        organizationalProcesses: perimetreItems
          .filter(i => i.referenceType === 'organizationalProcess' && i.id !== item.id)
          .map(i => i.referenceId),
        controls: perimetreItems
          .filter(i => i.referenceType === 'control' && i.id !== item.id)
          .map(i => i.referenceId),
        businessProcesses: perimetreItems
          .filter(i => i.referenceType === 'businessProcess' && i.id !== item.id)
          .map(i => i.referenceId)
      };

      // Update relationships in backend
      const response = await updateAuditScopeRelationships(missionAudit.id, currentRelationships);
      if (response.success) {
        // Update local state
        setPerimetreItems(prev => prev.filter(i => i.id !== item.id));
        toast.success("Élément délié avec succès");
        await fetchAuditScope(); // Always refresh from backend after unlink
      } else {
        throw new Error(response.message || 'Erreur lors du déliage');
      }
    } catch (error) {
      console.error('Error unlinking item:', error);
      toast.error(error.message || 'Erreur lors du déliage de l\'élément');
    } finally {
      setUnlinkingItemId(null);
    }
  };

  // Handle perimetre input changes
  const handlePerimetreInputChange = (e) => {
    const { name, value } = e.target;
    setNewPerimetreItem(prev => ({ ...prev, [name]: value }));
  };

  const handlePerimetreSelectChange = (name, value) => {
    setNewPerimetreItem(prev => ({ ...prev, [name]: value }));
  };

  // Handle adding perimetre item
  const handleAddPerimetreItem = () => {
    if (!newPerimetreItem.nom) {
      toast.error("Veuillez remplir le nom de l'élément");
      return;
    }
    if (editingPerimetreItem) {
      setPerimetreItems(prev => prev.map(item =>
        item.id === editingPerimetreItem.id ? { ...newPerimetreItem, id: item.id } : item
      ));
      toast.success("Élément du périmètre mis à jour avec succès");
    } else {
      setPerimetreItems(prev => [...prev, { ...newPerimetreItem, id: Date.now() }]);
      toast.success("Nouvel élément ajouté au périmètre");
    }
    setNewPerimetreItem({ nom: "", type: "Processus" });
    setEditingPerimetreItem(null);
    setIsPerimetreDialogOpen(false);
  };

  // Handle editing perimetre item
  const handleEditPerimetreItem = (item) => {
    setEditingPerimetreItem(item);
    setNewPerimetreItem(item);
    setIsPerimetreDialogOpen(true);
  };

  // Auto-save function using Redux
  const autoSave = useCallback(
    async (currentCharacteristics) => {
      if (!missionAudit?.id || !isMountedRef.current) return;
      if (!currentCharacteristics.title.trim()) return;
      if (!codeValidation.isValid && currentCharacteristics.code.trim()) return;
      setIsAutoSaving(true);
      try {
        const updateData = {
          name: currentCharacteristics.title,
          code: currentCharacteristics.code,
          categorie: currentCharacteristics.category,
          etat: currentCharacteristics.status,
          planifieInitialement: currentCharacteristics.includedInInitialPlan,
          objectif: currentCharacteristics.objectives,
          evaluation: currentCharacteristics.evaluation,
          avancement: currentCharacteristics.progression,
        };
        await dispatch(updateAuditMission({ id: missionAudit.id, missionData: updateData })).unwrap();
      } catch (error) {
        if (error && error.name !== 'CanceledError') {
          toast.error(error.message || 'Erreur lors de la sauvegarde');
        }
      } finally {
        if (isMountedRef.current) setIsAutoSaving(false);
      }
    },
    [dispatch, missionAudit, codeValidation.isValid]
  );

  // Debounced code validation
  const validateCode = useCallback(async (code) => {
    if (!code.trim()) {
      setCodeValidation({ isValid: true, message: '', isChecking: false });
      return true;
    }

    setCodeValidation(prev => ({ ...prev, isChecking: true }));

    try {
      const response = await axios.get(`${getApiBaseUrl()}/audit-missions/validate-code/${encodeURIComponent(code)}`, {
        params: { excludeId: missionAudit?.id }
      });

      const isValid = response.data.success && response.data.isUnique;
      setCodeValidation({
        isValid,
        message: isValid ? '' : 'Ce code est déjà utilisé par une autre mission',
        isChecking: false
      });

      return isValid;
    } catch (error) {
      console.error('Error validating code:', error);
      setCodeValidation({
        isValid: false,
        message: 'Erreur lors de la validation du code',
        isChecking: false
      });
      return false;
    }
  }, [missionAudit?.id]);

  const debouncedCodeValidation = useMemo(() => debounce(validateCode, 800), [validateCode]);

  // Filter perimetre items
  const filteredPerimetreItems = perimetreItems.filter(item =>
    item.nom.toLowerCase().includes(perimetreSearch.toLowerCase())
  );

  // Debounced auto-save (move this above the loading check)
  const debouncedAutoSave = useMemo(() => debounce(autoSave, 1000), [autoSave]);

  // Replace the loading check:
  if (initialLoad || !missionAudit || !missionAudit.id || isLoadingUsers) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F62D51]"></div>
      </div>
    );
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, [name]: value };
      if (name === 'code') {
        debouncedCodeValidation(value);
      }
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  const handleSelectChange = (name, value) => {
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, [name]: value };
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  const handleCheckboxChange = (name, checked) => {
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, [name]: checked };
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  return (
    <ErrorBoundary>
      <div className="space-y-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
            <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
            Contexte de la Mission
          </h2>
          <div className="flex items-center gap-2">
            {isAutoSaving || missionLoading ? (
              <div className="flex items-center text-sm text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-600 mr-2" />
                Sauvegarde automatique...
              </div>
            ) : null}
          </div>
        </div>

        {/* Section Caractéristiques */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
            onClick={() => setIsCaracteristiquesOpen(!isCaracteristiquesOpen)}
          >
            <div className="flex items-center gap-2">
              {isCaracteristiquesOpen ? (
                <ChevronUp className="h-5 w-5 text-blue-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-blue-600" />
              )}
              <FileText className="h-5 w-5 text-blue-600 mr-1" />
              <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
            </div>
          </button>

          {isCaracteristiquesOpen && (
            <div className="p-5 bg-white">
              {/* Plan d'audit label above the form */}
              <div className="mb-4">
                <span className="text-base font-semibold text-blue-800 flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  Plan d'audit : <span className="ml-1 text-blue-900">{characteristics.auditPlan || <span className="text-gray-400">Non renseigné</span>}</span>
                </span>
              </div>
              <div className="grid grid-cols-4 gap-4">
                {/* First row: Nom and Code */}
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="title">Nom de la mission *</Label>
                  <Input 
                    id="title" 
                    name="title"
                    value={characteristics.title}
                    onChange={handleInputChange}
                    placeholder="Nom de la mission d'audit"
                    className="w-full"
                  />
                </div>
                <div className="col-span-1 space-y-2">
                  <Label htmlFor="code">Code *</Label>
                  <div className="relative">
                    <Input
                      id="code"
                      name="code"
                      value={characteristics.code}
                      onChange={handleInputChange}
                      placeholder="Code"
                      className={`w-full ${!codeValidation.isValid ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    {codeValidation.isChecking && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-400" />
                      </div>
                    )}
                  </div>
                  {!codeValidation.isValid && codeValidation.message && (
                    <p className="text-sm text-red-600">{codeValidation.message}</p>
                  )}
                </div>
                {/* Second row: Catégorie (2/4), Inclus dans le plan initial (1/4), Statut (1/4) */}
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="category">Catégorie</Label>
                  <Select 
                    name="category"
                    value={characteristics.category} 
                    onValueChange={(value) => handleSelectChange("category", value)}
                    className="w-full"
                  >
                    <SelectTrigger id="category" className="w-full">
                      <SelectValue placeholder="Sélectionner une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Conformité">Conformité</SelectItem>
                      <SelectItem value="Performance">Efficacité</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-1 flex items-end space-y-2">
                  <div className="flex items-center space-x-2 h-10 pt-4 w-full">
                    <Checkbox 
                      id="includedInInitialPlan" 
                      checked={characteristics.includedInInitialPlan}
                      onCheckedChange={(checked) => handleCheckboxChange("includedInInitialPlan", checked)}
                    />
                    <Label htmlFor="includedInInitialPlan">Inclus dans le plan initial</Label>
                  </div>
                </div>
                <div className="col-span-1 space-y-2">
                  <Label htmlFor="status">État</Label>
                  <Select 
                    name="status"
                    value={characteristics.status} 
                    onValueChange={(value) => handleSelectChange("status", value)}
                    className="w-full"
                  >
                    <SelectTrigger id="status" className="w-full">
                      <SelectValue placeholder="Sélectionner un statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Planifiée">Planifiée</SelectItem>
                      <SelectItem value="En cours">En cours</SelectItem>
                      <SelectItem value="Terminée">Terminée</SelectItem>
                      <SelectItem value="Suspendue">Suspendue</SelectItem>
                      <SelectItem value="Annulée">Annulée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                  
                {/* Third row */}
                {/* Fourth row - Chef de mission */}
                
                
                <div className="col-span-4 space-y-2">
                  <Label htmlFor="objectives">Objectif *</Label>
                  <Textarea 
                    id="objectives" 
                    name="objectives"
                    value={characteristics.objectives}
                    onChange={handleInputChange}
                    placeholder="Objectifs principaux de la mission d'audit"
                    rows={3}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Section Périmètre */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg"
            onClick={() => setIsPerimetreOpen(!isPerimetreOpen)}
          >
            <div className="flex items-center gap-2">
              {isPerimetreOpen ? <ChevronUp className="h-5 w-5 text-purple-600" /> : <ChevronDown className="h-5 w-5 text-purple-600" />}
              <Map className="h-5 w-5 text-purple-600 mr-1" />
              <span className="text-lg font-medium text-purple-800">Périmètre</span>
            </div>
          </button>
          {isPerimetreOpen && (
            <div className="p-5 bg-white">
              <div className="mb-4 flex justify-between items-center">
                <div className="relative w-64">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Rechercher dans le périmètre..."
                    className="pl-8"
                    value={perimetreSearch}
                    onChange={e => setPerimetreSearch(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                    onClick={() => setIsLinkDialogOpen(true)}
                  >
                    <Link className="h-4 w-4 mr-2" />
                    Relier
                  </Button>
                </div>
              </div>
              <Card className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <Table className="min-w-full">
                      <TableHeader>
                        <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                          <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                          <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</TableHead>
                          <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody className="divide-y divide-gray-200">
                        {isLoadingPerimetre ? (
                          <TableRow>
                            <TableCell colSpan={3} className="px-4 py-10 text-center">
                              <div className="flex items-center justify-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                              </div>
                            </TableCell>
                          </TableRow>
                        ) : filteredPerimetreItems.length > 0 ? (
                          filteredPerimetreItems.map(item => (
                            <TableRow key={item.id} className="hover:bg-gray-50/50">
                              <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                                <div className="flex items-center">
                                  {getPerimetreIcon(item.type, item.referenceType)}
                                  <span>{item.nom}</span>
                                </div>
                              </TableCell>
                              <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{getTypeBadge(item.type)}</TableCell>
                              <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-8 w-8 p-0"
                                    onClick={() => handleEditPerimetreItem(item)}
                                  >
                                    <Edit className="h-4 w-4 text-blue-600" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-8 w-8 p-0"
                                    onClick={() => handleUnlinkPerimetreItem(item)}
                                    title="Délier l'élément"
                                    disabled={unlinkingItemId === item.id}
                                  >
                                    {unlinkingItemId === item.id ? (
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                                    ) : (
                                      <BrokenChain className="h-4 w-4 text-red-600" />
                                    )}
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={3} className="px-4 py-10 text-center text-sm text-gray-500">
                              Aucun élément dans le périmètre
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>



        {/* Section Jalons */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-violet-50 rounded-t-lg"
            onClick={() => setIsJalonsOpen(!isJalonsOpen)}
          >
            <div className="flex items-center gap-2">
              {isJalonsOpen ? (
                <ChevronUp className="h-5 w-5 text-green-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-green-600" />
              )}
              <Clock className="h-5 w-5 text-green-600 mr-1" />
              <span className="text-lg font-medium text-green-800">Jalons</span>
            </div>
          </button>

          {isJalonsOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>




        {/* Link Dialog */}
        <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
          <DialogContent className="sm:max-w-[1000px] max-h-[80vh] flex flex-col min-h-[500px]" aria-describedby="link-dialog-description">
            <DialogHeader>
              <DialogTitle>Sélectionnez et relier au périmètre de l'audit</DialogTitle>
              <DialogDescription id="link-dialog-description">
                Choisissez les éléments à associer au périmètre de votre mission d'audit.
              </DialogDescription>
            </DialogHeader>
            <div className="sticky top-0 z-10 bg-white pt-2 pb-2">
              <div className="flex border-b space-x-2">
                <button
                  className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'entities' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('entities')}
                >
                  <div className="flex items-center justify-center">Entités</div>
                </button>
                <button
                  className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'risks' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('risks')}
                >
                  <div className="flex items-center justify-center">Risques</div>
                </button>
                <button
                  className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'businessProcesses' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('businessProcesses')}
                >
                  <div className="flex items-center justify-center">Processus métier</div>
                </button>
                <button
                  className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'organizationalProcesses' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('organizationalProcesses')}
                >
                  <div className="flex items-center justify-center">Processus org.</div>
                </button>
                <button
                  className={`px-2 py-2 font-medium flex-1 text-center whitespace-nowrap text-sm ${activeTab === 'controls' ? 'text-[#F62D51] border-b-2 border-[#F62D51]' : 'text-gray-500'}`}
                  onClick={() => setActiveTab('controls')}
                >
                  <div className="flex items-center justify-center">Contrôles</div>
                </button>
              </div>
              <div className="relative w-full mt-2">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input placeholder="Rechercher..." className="pl-8" value={linkSearch} onChange={e => setLinkSearch(e.target.value)} />
              </div>
            </div>
            <div className="flex-1 min-h-0 overflow-y-auto py-4">
              {isLoadingLinkData ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                </div>
              ) : (
                <>
                  {activeTab === "entities" && (
                    <div key="entities-list" className="space-y-2">
                      {entities.length === 0 ? (
                        <p className="text-gray-500 text-center py-8">Aucune entité trouvée</p>
                      ) : (
                        entities.filter(e => e.name.toLowerCase().includes(linkSearch.toLowerCase())).map((entity, index) => (
                          <div key={entity.entityID || `entity-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                            <Checkbox
                              id={`entity-${entity.entityID || index}`}
                              checked={selectedItems.entities.includes(String(entity.entityID))}
                              onCheckedChange={checked => {
                                setSelectedItems(prev => ({
                                  ...prev,
                                  entities: checked
                                    ? [...prev.entities, String(entity.entityID)]
                                    : prev.entities.filter(id => id !== String(entity.entityID))
                                }));
                              }}
                            />
                            <label htmlFor={`entity-${entity.entityID || index}`} className="flex-1 cursor-pointer">
                              {entity.name}
                            </label>
                          </div>
                        ))
                      )}
                    </div>
                  )}

                  {activeTab === "risks" && (
                    <div key="risks-list" className="space-y-2">
                      {risks.length === 0 ? (
                        <p className="text-gray-500 text-center py-8">Aucun risque trouvé</p>
                      ) : (
                        risks.filter(r => r.name.toLowerCase().includes(linkSearch.toLowerCase())).map((risk, index) => (
                          <div key={risk.riskID || `risk-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                            <Checkbox
                              id={`risk-${risk.riskID || index}`}
                              checked={selectedItems.risks.includes(String(risk.riskID))}
                              onCheckedChange={checked => {
                                setSelectedItems(prev => ({
                                  ...prev,
                                  risks: checked
                                    ? [...prev.risks, String(risk.riskID)]
                                    : prev.risks.filter(id => id !== String(risk.riskID))
                                }));
                              }}
                            />
                            <label htmlFor={`risk-${risk.riskID || index}`} className="flex-1 cursor-pointer">
                              {risk.name}
                            </label>
                          </div>
                        ))
                      )}
                    </div>
                  )}

                  {activeTab === "businessProcesses" && (
                    <div key="businessProcesses-list" className="space-y-2">
                      {businessProcesses.length === 0 ? (
                        <p className="text-gray-500 text-center py-8">Aucun processus métier trouvé</p>
                      ) : (
                        businessProcesses.filter(p => p.name.toLowerCase().includes(linkSearch.toLowerCase())).map((process, index) => (
                          <div key={process.businessProcessID || `businessProcess-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                            <Checkbox
                              id={`businessProcess-${process.businessProcessID || index}`}
                              checked={selectedItems.businessProcesses.includes(String(process.businessProcessID))}
                              onCheckedChange={checked => {
                                setSelectedItems(prev => ({
                                  ...prev,
                                  businessProcesses: checked
                                    ? [...prev.businessProcesses, String(process.businessProcessID)]
                                    : prev.businessProcesses.filter(id => id !== String(process.businessProcessID))
                                }));
                              }}
                            />
                            <label htmlFor={`businessProcess-${process.businessProcessID || index}`} className="flex-1 cursor-pointer">
                              {process.name}
                            </label>
                          </div>
                        ))
                      )}
                    </div>
                  )}

                  {activeTab === "organizationalProcesses" && (
                    <div key="organizationalProcesses-list" className="space-y-2">
                      {organizationalProcesses.length === 0 ? (
                        <p className="text-gray-500 text-center py-8">Aucun processus organisationnel trouvé</p>
                      ) : (
                        organizationalProcesses.filter(p => p.name.toLowerCase().includes(linkSearch.toLowerCase())).map((process, index) => (
                          <div key={process.organizationalProcessID || `organizationalProcess-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                            <Checkbox
                              id={`organizationalProcess-${process.organizationalProcessID || index}`}
                              checked={selectedItems.organizationalProcesses.includes(String(process.organizationalProcessID))}
                              onCheckedChange={checked => {
                                setSelectedItems(prev => ({
                                  ...prev,
                                  organizationalProcesses: checked
                                    ? [...prev.organizationalProcesses, String(process.organizationalProcessID)]
                                    : prev.organizationalProcesses.filter(id => id !== String(process.organizationalProcessID))
                                }));
                              }}
                            />
                            <label htmlFor={`organizationalProcess-${process.organizationalProcessID || index}`} className="flex-1 cursor-pointer">
                              {process.name}
                            </label>
                          </div>
                        ))
                      )}
                    </div>
                  )}

                  {activeTab === "controls" && (
                    <div key="controls-list" className="space-y-2">
                      {controls.length === 0 ? (
                        <p className="text-gray-500 text-center py-8">Aucun contrôle trouvé</p>
                      ) : (
                        controls.filter(c => c.name.toLowerCase().includes(linkSearch.toLowerCase())).map((control, index) => (
                          <div key={control.controlID || `control-${index}`} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                            <Checkbox
                              id={`control-${control.controlID || index}`}
                              checked={selectedItems.controls.includes(String(control.controlID))}
                              onCheckedChange={checked => {
                                setSelectedItems(prev => ({
                                  ...prev,
                                  controls: checked
                                    ? [...prev.controls, String(control.controlID)]
                                    : prev.controls.filter(id => id !== String(control.controlID))
                                }));
                              }}
                            />
                            <label htmlFor={`control-${control.controlID || index}`} className="flex-1 cursor-pointer">
                              {control.name}
                            </label>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsLinkDialogOpen(false)}>
                Annuler
              </Button>
              <Button
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                onClick={async () => {
                  // Update perimetre items based on selected items
                  const newPerimetreItems = [];

                  // Add selected entities
                  selectedItems.entities.forEach(entityId => {
                    const entity = entities.find(e => String(e.entityID) === entityId);
                    if (entity) {
                      newPerimetreItems.push({
                        id: `entity-${entity.entityID}`,
                        nom: entity.name,
                        type: "Entité",
                        referenceId: entity.entityID,
                        referenceType: "entity"
                      });
                    }
                  });

                  // Add selected risks
                  selectedItems.risks.forEach(riskId => {
                    const risk = risks.find(r => String(r.riskID) === riskId);
                    if (risk) {
                      newPerimetreItems.push({
                        id: `risk-${risk.riskID}`,
                        nom: risk.name,
                        type: "Risque",
                        referenceId: risk.riskID,
                        referenceType: "risk"
                      });
                    }
                  });

                  // Add selected business processes
                  selectedItems.businessProcesses.forEach(processId => {
                    const process = businessProcesses.find(p => String(p.businessProcessID) === processId);
                    if (process) {
                      newPerimetreItems.push({
                        id: `businessProcess-${process.businessProcessID}`,
                        nom: process.name,
                        type: "Processus métier",
                        referenceId: process.businessProcessID,
                        referenceType: "businessProcess"
                      });
                    }
                  });

                  // Add selected organizational processes
                  selectedItems.organizationalProcesses.forEach(processId => {
                    const process = organizationalProcesses.find(p => String(p.organizationalProcessID) === processId);
                    if (process) {
                      newPerimetreItems.push({
                        id: `organizationalProcess-${process.organizationalProcessID}`,
                        nom: process.name,
                        type: "Processus organisationnel",
                        referenceId: process.organizationalProcessID,
                        referenceType: "organizationalProcess"
                      });
                    }
                  });

                  // Add selected controls
                  selectedItems.controls.forEach(controlId => {
                    const control = controls.find(c => String(c.controlID) === controlId);
                    if (control) {
                      newPerimetreItems.push({
                        id: `control-${control.controlID}`,
                        nom: control.name,
                        type: "Contrôle",
                        referenceId: control.controlID,
                        referenceType: "control"
                      });
                    }
                  });

                  if (newPerimetreItems.length > 0) {
                    // Transform all items into the format expected by the API
                    const relationships = {
                      entities: [...perimetreItems, ...newPerimetreItems]
                        .filter(item => item.referenceType === 'entity')
                        .map(item => item.referenceId),
                      risks: [...perimetreItems, ...newPerimetreItems]
                        .filter(item => item.referenceType === 'risk')
                        .map(item => item.referenceId),
                      organizationalProcesses: [...perimetreItems, ...newPerimetreItems]
                        .filter(item => item.referenceType === 'organizationalProcess')
                        .map(item => item.referenceId),
                      controls: [...perimetreItems, ...newPerimetreItems]
                        .filter(item => item.referenceType === 'control')
                        .map(item => item.referenceId),
                      businessProcesses: [...perimetreItems, ...newPerimetreItems]
                        .filter(item => item.referenceType === 'businessProcess')
                        .map(item => item.referenceId)
                    };

                    console.log('Sending relationships to backend:', relationships);

                    try {
                      // Save to backend
                      const response = await updateAuditScopeRelationships(missionAudit.id, relationships);
                      if (response.success) {
                        toast.success(`${newPerimetreItems.length} élément(s) ajouté(s) au périmètre`);
                        await fetchAuditScope(); // Always refresh from backend after link
                        // Close modal and reset selections only on success
                        setIsLinkDialogOpen(false);
                        setSelectedItems({ 
                          entities: [], 
                          risks: [], 
                          organizationalProcesses: [], 
                          controls: [],
                          businessProcesses: []
                        });
                      } else {
                        throw new Error(response.message || 'Erreur lors de la sauvegarde');
                      }
                    } catch (error) {
                      console.error('Error saving relationships:', error);
                      toast.error(error.message || 'Erreur lors de la sauvegarde des relations');
                    }
                  } else {
                    toast.info("Aucun nouvel élément à ajouter");
                    // Close modal even if no new items to add
                    setIsLinkDialogOpen(false);
                    setSelectedItems({ 
                      entities: [], 
                      risks: [], 
                      organizationalProcesses: [], 
                      controls: [],
                      businessProcesses: []
                    });
                  }
                }}
              >
                Relier les éléments sélectionnés
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Perimetre Edit Dialog */}
        <Dialog open={isPerimetreDialogOpen} onOpenChange={setIsPerimetreDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{editingPerimetreItem ? "Modifier l'élément du périmètre" : "Ajouter au périmètre"}</DialogTitle>
              <DialogDescription>
                {editingPerimetreItem ? "Modifiez les informations de l'élément sélectionné" : "Ajoutez un nouvel élément au périmètre de l'audit"}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="nom">Nom *</Label>
                <Input
                  id="nom"
                  name="nom"
                  value={newPerimetreItem.nom}
                  onChange={handlePerimetreInputChange}
                  placeholder="Nom de l'élément"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select
                  name="type"
                  value={newPerimetreItem.type}
                  onValueChange={value => handlePerimetreSelectChange("type", value)}
                >
                  <SelectTrigger id="type" className="w-full">
                    <SelectValue placeholder="Sélectionner un type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Processus">Processus</SelectItem>
                    <SelectItem value="Système">Système</SelectItem>
                    <SelectItem value="Organisation">Organisation</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsPerimetreDialogOpen(false);
                  setEditingPerimetreItem(null);
                  setNewPerimetreItem({ nom: "", type: "Processus" });
                }}
              >
                Annuler
              </Button>
              <Button
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                onClick={handleAddPerimetreItem}
              >
                {editingPerimetreItem ? "Mettre à jour" : "Ajouter"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </ErrorBoundary>
  );
}

export default CaracteristiquesTab; 