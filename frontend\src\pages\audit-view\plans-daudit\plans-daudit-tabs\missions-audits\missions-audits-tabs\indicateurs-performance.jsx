import React, { useState, useEffect, useCallback } from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FileText, FileDown, Loader2, Alert<PERSON>riangle, RefreshCw, Mail } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import EmailReportModal from '@/components/reports/EmailReportModal';

import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
ChartJS.register(ArcElement, Tooltip, Legend);
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";

function IndicateursPerformanceTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;


  // State for report functionality
  const [reportData, setReportData] = useState(null);
  const [loadingReport, setLoadingReport] = useState(false);
  const [downloadingPdf, setDownloadingPdf] = useState(false);
  const [downloadingDocx, setDownloadingDocx] = useState(false);
  const [reportError, setReportError] = useState(null);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);

  // Fetch report data
  const fetchReportData = useCallback(async () => {
    if (!missionAudit?.id) return;

    setLoadingReport(true);
    setReportError(null);

    try {
      // Fetch data from the correct existing endpoints
      const [missionReportResponse, activitiesResponse, recommendationsResponse, constatsResponse] = await Promise.all([
        axios.get(`${getApiBaseUrl()}/audit-mission-rapports/mission-report/${missionAudit.id}`, { withCredentials: true }),
        axios.get(`${getApiBaseUrl()}/rapport/mission-charts/${missionAudit.id}/activities`, { withCredentials: true }),
        axios.get(`${getApiBaseUrl()}/rapport/mission-charts/${missionAudit.id}/recommendations`, { withCredentials: true }),
        axios.get(`${getApiBaseUrl()}/rapport/mission-charts/${missionAudit.id}/constats`, { withCredentials: true })
      ]);

      // Process and combine the responses based on actual API structure
      const reportData = missionReportResponse.data;
      const activitiesData = activitiesResponse.data?.data || [];
      const recommendationsData = recommendationsResponse.data?.data || [];
      const constatsData = constatsResponse.data?.data || [];

      // Calculate statistics from the chart data
      const totalActivities = activitiesData.reduce((sum, item) => sum + (item.count || 0), 0);
      const completedActivities = activitiesData.find(item =>
        item.status && (item.status.toLowerCase().includes('completed') || item.status.toLowerCase().includes('terminé'))
      )?.count || 0;

      const totalRecommendations = recommendationsData.reduce((sum, item) => sum + (item.count || 0), 0);
      const implementedRecommendations = recommendationsData.filter(item =>
        item.recommendation_status && item.recommendation_status.toLowerCase().includes('implemented')
      ).reduce((sum, item) => sum + (item.count || 0), 0);

      const totalConstats = constatsData.reduce((sum, item) => sum + (item.count || 0), 0);
      const criticalConstats = constatsData.filter(item =>
        item.type && item.type.toLowerCase().includes('critique')
      ).reduce((sum, item) => sum + (item.count || 0), 0);

      // Build comprehensive report data
      const combinedReportData = {
        statistics: {
          completedActivities,
          totalActivities,
          totalConstats,
          criticalConstats,
          totalRecommendations,
          implementedRecommendations,
          progressPercentage: totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0
        },
        charts: {
          activitiesChart: {
            labels: activitiesData.map(d => d.status || 'N/A'),
            datasets: [{
              data: activitiesData.map(d => d.count),
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40']
            }]
          },
          constatsChart: {
            labels: constatsData.map(d => d.type || 'N/A'),
            datasets: [{
              data: constatsData.map(d => d.count),
              backgroundColor: ['#22c55e', '#ef4444']
            }]
          },
          recommendationsChart: {
            labels: recommendationsData.map(d => d.recommendation_status || 'N/A'),
            datasets: [{
              data: recommendationsData.map(d => d.count),
              backgroundColor: ['#10b981', '#f59e0b', '#ef4444']
            }]
          }
        },
        missionReport: reportData
      };

      setReportData(combinedReportData);
    } catch (error) {
      console.error('Error fetching report:', error);
      setReportError(error.response?.data?.message || 'Erreur lors du chargement du rapport');
    } finally {
      setLoadingReport(false);
    }
  }, [missionAudit?.id]);

  useEffect(() => {
    fetchReportData();
  }, [fetchReportData]);

  // Generate PDF report
  const generatePdfReport = async () => {
    if (!reportData) return;

    setDownloadingPdf(true);
    try {
      const pdf = new jsPDF();
      
      // Add title
      pdf.setFontSize(20);
      pdf.text('Indicateurs de Performance - Mission d\'Audit', 20, 30);
      
      // Add mission info
      pdf.setFontSize(12);
      pdf.text(`Mission: ${missionAudit.name}`, 20, 50);
      pdf.text(`Date: ${new Date().toLocaleDateString('fr-FR')}`, 20, 60);
      
      // Add statistics
      let yPosition = 80;
      pdf.setFontSize(14);
      pdf.text('Statistiques:', 20, yPosition);
      yPosition += 20;
      
      pdf.setFontSize(10);
      if (reportData.statistics) {
        Object.entries(reportData.statistics).forEach(([key, value]) => {
          pdf.text(`${key}: ${value}`, 20, yPosition);
          yPosition += 10;
        });
      }
      
      pdf.save(`indicateurs-performance-${missionAudit.name}-${new Date().toISOString().split('T')[0]}.pdf`);
      toast.success('Rapport PDF généré avec succès');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Erreur lors de la génération du PDF');
    } finally {
      setDownloadingPdf(false);
    }
  };

  // Generate Excel report
  const generateExcelReport = async () => {
    if (!reportData) return;

    setDownloadingDocx(true);
    try {
      const workbook = XLSX.utils.book_new();
      
      // Create summary sheet
      const summaryData = [
        ['Mission', missionAudit.name],
        ['Date', new Date().toLocaleDateString('fr-FR')],
        ['', ''],
        ['Indicateurs de Performance', '']
      ];
      
      if (reportData.statistics) {
        Object.entries(reportData.statistics).forEach(([key, value]) => {
          summaryData.push([key, value]);
        });
      }
      
      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Résumé');
      
      // Save file
      XLSX.writeFile(workbook, `indicateurs-performance-${missionAudit.name}-${new Date().toISOString().split('T')[0]}.xlsx`);
      toast.success('Rapport Excel généré avec succès');
    } catch (error) {
      console.error('Error generating Excel:', error);
      toast.error('Erreur lors de la génération du fichier Excel');
    } finally {
      setDownloadingDocx(false);
    }
  };



  if (!missionAudit) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations de la mission...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Indicateurs de Performance</h2>
          <p className="text-gray-600 mt-1">Mission: {missionAudit.name}</p>
        </div>
        <Button
          onClick={fetchReportData}
          disabled={loadingReport}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loadingReport ? 'animate-spin' : ''}`} />
          Actualiser
        </Button>
      </div>

      {/* Loading State */}
      {loadingReport && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mr-3" />
            <p className="text-gray-600">Chargement des indicateurs de performance...</p>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {reportError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="flex items-center py-4">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-3" />
            <p className="text-red-800">{reportError}</p>
          </CardContent>
        </Card>
      )}

      {/* Report Content */}
      {reportData && !loadingReport && (
        <>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={generatePdfReport}
              disabled={downloadingPdf}
              variant="outline"
              className="flex items-center gap-2"
            >
              {downloadingPdf ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <FileDown className="h-4 w-4" />
              )}
              PDF
            </Button>
            <Button
              onClick={generateExcelReport}
              disabled={downloadingDocx}
              variant="outline"
              className="flex items-center gap-2"
            >
              {downloadingDocx ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <FileDown className="h-4 w-4" />
              )}
              Excel
            </Button>
            <Button
              onClick={() => setIsEmailModalOpen(true)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Mail className="h-4 w-4" />
              Envoyer
            </Button>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Activités Complétées</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {reportData.statistics?.completedActivities || 0}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  sur {reportData.statistics?.totalActivities || 0} activités
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Constats Identifiés</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {reportData.statistics?.totalConstats || 0}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.statistics?.criticalConstats || 0} critiques
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Recommandations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {reportData.statistics?.totalRecommendations || 0}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.statistics?.implementedRecommendations || 0} mises en œuvre
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Taux d'Avancement</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {reportData.statistics?.progressPercentage || 0}%
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  de la mission
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          {reportData.charts && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {reportData.charts.activitiesChart && (
                <Card>
                  <CardHeader>
                    <CardTitle>Répartition des Activités</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <Doughnut
                        data={reportData.charts.activitiesChart}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom',
                            },
                          },
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {reportData.charts.constatsChart && (
                <Card>
                  <CardHeader>
                    <CardTitle>Répartition des Constats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <Doughnut
                        data={reportData.charts.constatsChart}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom',
                            },
                          },
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </>
      )}



      {/* Email Modal */}
      {isEmailModalOpen && (
        <EmailReportModal
          isOpen={isEmailModalOpen}
          onClose={() => setIsEmailModalOpen(false)}
          reportType="indicateurs-performance"
          missionId={missionAudit.id}
          missionName={missionAudit.name}
        />
      )}
    </div>
  );
}

export default IndicateursPerformanceTab;
