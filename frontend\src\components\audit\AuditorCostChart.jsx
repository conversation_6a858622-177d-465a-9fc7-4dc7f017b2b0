import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users, Clock } from "lucide-react";

const AuditorCostChart = ({ timeEntries, auditors }) => {
  // Calculate cost breakdown by auditor
  const getCostBreakdown = () => {
    const breakdown = {};
    timeEntries.forEach(entry => {
      if (!breakdown[entry.auditorId]) {
        breakdown[entry.auditorId] = {
          name: entry.auditorName,
          totalCost: 0,
          totalHours: 0,
          entries: 0
        };
      }
      breakdown[entry.auditorId].totalCost += entry.totalCost;
      breakdown[entry.auditorId].totalHours += entry.totalHours;
      breakdown[entry.auditorId].entries += 1;
    });
    return Object.values(breakdown);
  };

  const costBreakdown = getCostBreakdown();
  const totalCost = costBreakdown.reduce((sum, item) => sum + item.totalCost, 0);
  const totalHours = costBreakdown.reduce((sum, item) => sum + item.totalHours, 0);

  // Generate colors for the chart
  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount);
  };

  if (costBreakdown.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart className="h-5 w-5" />
            Répartition des Coûts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <PieChart className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>Aucune donnée de coût disponible</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart className="h-5 w-5" />
          Répartition des Coûts par Auditeur
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{formatCurrency(totalCost)}</div>
              <div className="text-sm text-blue-600">Coût Total</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{totalHours}h</div>
              <div className="text-sm text-green-600">Temps Total</div>
            </div>
          </div>

          {/* Cost Breakdown Bars */}
          <div className="space-y-3">
            {costBreakdown.map((item, index) => {
              const percentage = totalCost > 0 ? (item.totalCost / totalCost) * 100 : 0;
              const color = colors[index % colors.length];
              
              return (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(item.totalCost)}</div>
                      <div className="text-xs text-gray-500 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {item.totalHours}h • {item.entries} entrées
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="h-3 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${percentage}%`,
                        backgroundColor: color
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 text-right">
                    {percentage.toFixed(1)}% du total
                  </div>
                </div>
              );
            })}
          </div>

          {/* Legend */}
          <div className="mt-6 pt-4 border-t">
            <div className="text-sm font-medium mb-2">Légende:</div>
            <div className="flex flex-wrap gap-3">
              {costBreakdown.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: colors[index % colors.length] }}
                  ></div>
                  <span className="text-xs text-gray-600">{item.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuditorCostChart;
