const express = require('express');
const router = express.Router();
const {
  getAllMissionsWithSkills,
  getMissionSkills,
  addSkillToMission,
  updateMissionSkill,
  removeSkillFromMission
} = require('../../controllers/audit/audit-mission-skills-controller');

// Middleware for authentication (assuming you have this)
const { authenticateToken } = require('../../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Routes
// GET /api/audit/mission-skills - Get all missions with their required skills
router.get('/', getAllMissionsWithSkills);

// GET /api/audit/mission-skills/:missionId - Get specific mission with its required skills
router.get('/:missionId', getMissionSkills);

// POST /api/audit/mission-skills/:missionId/skills - Add skill to mission
router.post('/:missionId/skills', addSkillToMission);

// PUT /api/audit/mission-skills/:missionId/skills/:skillId - Update mission skill requirement
router.put('/:missionId/skills/:skillId', updateMissionSkill);

// DELETE /api/audit/mission-skills/:missionId/skills/:skillId - Remove skill from mission
router.delete('/:missionId/skills/:skillId', removeSkillFromMission);

module.exports = router;
