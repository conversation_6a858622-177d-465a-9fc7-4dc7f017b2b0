import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronUp, ChevronDown, DollarSign, Loader2, Edit, X } from "lucide-react";
import { toast } from "react-hot-toast";
import axios from 'axios';
import { getApiBaseUrl } from "@/utils/api-config";
import { useMissionAuditContext } from '@/utils/context-helpers';

function DepensesTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;

  // State management for charges
  const [charges, setCharges] = useState([]);
  const [isLoadingCharges, setIsLoadingCharges] = useState(false);
  const [isChargeDialogOpen, setIsChargeDialogOpen] = useState(false);
  const [editingCharge, setEditingCharge] = useState(null);
  const [isChargesOpen, setIsChargesOpen] = useState(true);
  const [chargesSummary, setChargesSummary] = useState({
    internalTotal: 0,
    externalTotal: 0,
    grandTotal: 0,
    internalCount: 0,
    externalCount: 0
  });
  const [newCharge, setNewCharge] = useState({
    ressourceName: '',
    prestataireName: '',
    serviceDescription: '',
    costPerUnit: '',
    duration: '',
    isExternal: false,
    currency: 'EUR'
  });

  // Utility functions for charges
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  const calculateChargeTotal = (charge) => {
    if (charge.isExternal) {
      return parseFloat(charge.costPerUnit || 0);
    } else {
      return parseFloat(charge.costPerUnit || 0) * parseFloat(charge.duration || 0);
    }
  };

  const getInternalCharges = () => charges.filter(charge => !charge.isExternal);
  const getExternalCharges = () => charges.filter(charge => charge.isExternal);

  const calculateSummary = (chargesData) => {
    const internal = chargesData.filter(c => !c.isExternal);
    const external = chargesData.filter(c => c.isExternal);

    const internalTotal = internal.reduce((sum, charge) => sum + calculateChargeTotal(charge), 0);
    const externalTotal = external.reduce((sum, charge) => sum + calculateChargeTotal(charge), 0);

    return {
      internalTotal,
      externalTotal,
      grandTotal: internalTotal + externalTotal,
      internalCount: internal.length,
      externalCount: external.length
    };
  };

  // API functions for charges
  const fetchMissionCharges = useCallback(async () => {
    if (!missionAudit?.id) return;

    setIsLoadingCharges(true);
    try {
      const response = await axios.get(
        `${getApiBaseUrl()}/audit/mission-charges/mission/${missionAudit.id}`,
        { withCredentials: true }
      );

      if (response.data.success) {
        setCharges(response.data.data.charges || []);
        setChargesSummary(calculateSummary(response.data.data.charges || []));
      } else {
        console.error('Failed to fetch charges');
        toast.error('Erreur lors du chargement des charges');
      }
    } catch (error) {
      console.error('Error fetching charges:', error);
      toast.error('Erreur lors du chargement des charges');
    } finally {
      setIsLoadingCharges(false);
    }
  }, [missionAudit?.id]);

  const handleSaveCharge = async () => {
    if (!missionAudit?.id) return;

    // Validation
    if (newCharge.isExternal) {
      if (!newCharge.prestataireName || !newCharge.serviceDescription || !newCharge.costPerUnit) {
        toast.error('Veuillez remplir tous les champs obligatoires');
        return;
      }
    } else {
      if (!newCharge.ressourceName || !newCharge.costPerUnit || !newCharge.duration) {
        toast.error('Veuillez remplir tous les champs obligatoires');
        return;
      }
    }

    try {
      const chargeData = {
        ...newCharge,
        costPerUnit: parseFloat(newCharge.costPerUnit),
        duration: newCharge.duration ? parseFloat(newCharge.duration) : null
      };

      let response;
      if (editingCharge) {
        response = await axios.put(
          `${getApiBaseUrl()}/audit/mission-charges/mission/${missionAudit.id}/charge/${editingCharge.id}`,
          chargeData,
          { withCredentials: true }
        );
      } else {
        response = await axios.post(
          `${getApiBaseUrl()}/audit/mission-charges/mission/${missionAudit.id}`,
          chargeData,
          { withCredentials: true }
        );
      }

      if (response.data.success) {
        toast.success(editingCharge ? 'Charge mise à jour avec succès' : 'Charge ajoutée avec succès');
        setIsChargeDialogOpen(false);
        setEditingCharge(null);
        setNewCharge({
          ressourceName: '',
          prestataireName: '',
          serviceDescription: '',
          costPerUnit: '',
          duration: '',
          isExternal: false,
          currency: 'EUR'
        });
        fetchMissionCharges();
      } else {
        toast.error(response.data.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Error saving charge:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de la sauvegarde');
    }
  };

  const handleDeleteCharge = async (chargeId) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette charge ?')) return;

    try {
      const response = await axios.delete(
        `${getApiBaseUrl()}/audit/mission-charges/mission/${missionAudit.id}/charge/${chargeId}`,
        { withCredentials: true }
      );

      if (response.data.success) {
        toast.success('Charge supprimée avec succès');
        fetchMissionCharges();
      } else {
        toast.error(response.data.message || 'Erreur lors de la suppression');
      }
    } catch (error) {
      console.error('Error deleting charge:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de la suppression');
    }
  };

  const handleEditCharge = (charge) => {
    setEditingCharge(charge);
    setNewCharge({
      ressourceName: charge.ressourceName || '',
      prestataireName: charge.prestataireName || '',
      serviceDescription: charge.serviceDescription || '',
      costPerUnit: charge.costPerUnit?.toString() || '',
      duration: charge.duration?.toString() || '',
      isExternal: charge.isExternal || false,
      currency: charge.currency || 'EUR'
    });
    setIsChargeDialogOpen(true);
  };

  const handleChargeInputChange = (field, value) => {
    setNewCharge(prev => ({ ...prev, [field]: value }));
  };

  // Effects
  useEffect(() => {
    if (missionAudit?.id && isChargesOpen) {
      fetchMissionCharges();
    }
  }, [missionAudit?.id, isChargesOpen, fetchMissionCharges]);

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des charges de la mission...</p>
      </div>
    );
  }



  return (
    <div className="space-y-6 py-4">
      {/* Section Charges de la mission */}
      <div className="border rounded-lg shadow-sm">
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-t-lg">
          <div className="flex items-center justify-between p-4">
            <button
              type="button"
              className="flex items-center gap-2 flex-1"
              onClick={() => setIsChargesOpen(!isChargesOpen)}
            >
              {isChargesOpen ? (
                <ChevronUp className="h-5 w-5 text-orange-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-orange-600" />
              )}
              <DollarSign className="h-5 w-5 text-orange-600 mr-1" />
              <span className="text-lg font-medium text-orange-800">Charges de la mission</span>
              <span className="ml-2 text-sm text-orange-600">
                ({chargesSummary.internalCount + chargesSummary.externalCount} charges • {formatCurrency(chargesSummary.grandTotal)})
              </span>
            </button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsChargeDialogOpen(true)}
              className="ml-4 border-orange-300 text-orange-700 hover:bg-orange-100"
            >
              <DollarSign className="h-4 w-4 mr-1" />
              Ajouter
            </Button>
          </div>
        </div>
        {isChargesOpen && (
          <div className="p-5 bg-white">
            {isLoadingCharges ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-orange-600 mr-2" />
                <span className="text-gray-600">Chargement des charges...</span>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Charge interne */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      Charges internes ({chargesSummary.internalCount})
                    </h4>
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-blue-50">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                                Ressource
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                                Coût/h (€)
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                                Durée (h)
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                                Total (€)
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {getInternalCharges().length === 0 ? (
                              <tr>
                                <td colSpan="5" className="px-4 py-8 text-center text-gray-500">
                                  Aucune charge interne enregistrée
                                </td>
                              </tr>
                            ) : (
                              <>
                                {getInternalCharges().map((charge, index) => (
                                  <tr key={charge.id} className={index % 2 === 1 ? "bg-gray-50" : ""}>
                                    <td className="px-4 py-3 text-sm text-gray-900 font-medium">
                                      {charge.ressourceName}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                      {parseFloat(charge.costPerUnit).toFixed(2)}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                      {parseFloat(charge.duration).toFixed(1)}
                                    </td>
                                    <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                      {formatCurrency(calculateChargeTotal(charge))}
                                    </td>
                                    <td className="px-4 py-3 text-sm">
                                      <div className="flex items-center gap-1">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleEditCharge(charge)}
                                          className="h-8 w-8 p-0 text-blue-600 hover:bg-blue-50"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleDeleteCharge(charge.id)}
                                          className="h-8 w-8 p-0 text-red-600 hover:bg-red-50"
                                        >
                                          <X className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </td>
                                  </tr>
                                ))}
                                <tr className="bg-blue-50">
                                  <td className="px-4 py-3 text-sm font-semibold text-blue-700">Total</td>
                                  <td className="px-4 py-3 text-sm text-gray-500">-</td>
                                  <td className="px-4 py-3 text-sm font-semibold text-blue-700">
                                    {getInternalCharges().reduce((sum, charge) => sum + parseFloat(charge.duration), 0).toFixed(1)}
                                  </td>
                                  <td className="px-4 py-3 text-sm font-semibold text-blue-700">
                                    {formatCurrency(chargesSummary.internalTotal)}
                                  </td>
                                  <td className="px-4 py-3"></td>
                                </tr>
                              </>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* Charge externe */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      Charges externes ({chargesSummary.externalCount})
                    </h4>
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-green-50">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                                Prestataire
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                                Service
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                                Coût total (€)
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {getExternalCharges().length === 0 ? (
                              <tr>
                                <td colSpan="4" className="px-4 py-8 text-center text-gray-500">
                                  Aucune charge externe enregistrée
                                </td>
                              </tr>
                            ) : (
                              <>
                                {getExternalCharges().map((charge, index) => (
                                  <tr key={charge.id} className={index % 2 === 1 ? "bg-gray-50" : ""}>
                                    <td className="px-4 py-3 text-sm text-gray-900 font-medium">
                                      {charge.prestataireName}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                      {charge.serviceDescription}
                                    </td>
                                    <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                      {formatCurrency(calculateChargeTotal(charge))}
                                    </td>
                                    <td className="px-4 py-3 text-sm">
                                      <div className="flex items-center gap-1">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleEditCharge(charge)}
                                          className="h-8 w-8 p-0 text-green-600 hover:bg-green-50"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleDeleteCharge(charge.id)}
                                          className="h-8 w-8 p-0 text-red-600 hover:bg-red-50"
                                        >
                                          <X className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </td>
                                  </tr>
                                ))}
                                <tr className="bg-green-50">
                                  <td className="px-4 py-3 text-sm font-semibold text-green-700">Total</td>
                                  <td className="px-4 py-3 text-sm text-gray-500">-</td>
                                  <td className="px-4 py-3 text-sm font-semibold text-green-700">
                                    {formatCurrency(chargesSummary.externalTotal)}
                                  </td>
                                  <td className="px-4 py-3"></td>
                                </tr>
                              </>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Summary */}
                <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-4 border border-orange-200">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{formatCurrency(chargesSummary.internalTotal)}</div>
                      <div className="text-sm text-gray-600">Charges internes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{formatCurrency(chargesSummary.externalTotal)}</div>
                      <div className="text-sm text-gray-600">Charges externes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">{formatCurrency(chargesSummary.grandTotal)}</div>
                      <div className="text-sm text-gray-600">Total général</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Charge Dialog */}
      <Dialog open={isChargeDialogOpen} onOpenChange={setIsChargeDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingCharge ? 'Modifier la charge' : 'Ajouter une charge'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Charge Type Selection */}
            <div className="space-y-2">
              <Label>Type de charge</Label>
              <Select
                value={newCharge.isExternal ? 'external' : 'internal'}
                onValueChange={(value) => handleChargeInputChange('isExternal', value === 'external')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner le type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="internal">Charge interne</SelectItem>
                  <SelectItem value="external">Charge externe</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Internal Charge Fields */}
            {!newCharge.isExternal && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="ressourceName">Ressource *</Label>
                  <Input
                    id="ressourceName"
                    value={newCharge.ressourceName}
                    onChange={(e) => handleChargeInputChange('ressourceName', e.target.value)}
                    placeholder="Ex: Auditeur Senior"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="costPerUnit">Coût/heure (€) *</Label>
                    <Input
                      id="costPerUnit"
                      type="number"
                      step="0.01"
                      value={newCharge.costPerUnit}
                      onChange={(e) => handleChargeInputChange('costPerUnit', e.target.value)}
                      placeholder="85.00"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="duration">Durée (h) *</Label>
                    <Input
                      id="duration"
                      type="number"
                      step="0.5"
                      value={newCharge.duration}
                      onChange={(e) => handleChargeInputChange('duration', e.target.value)}
                      placeholder="40"
                    />
                  </div>
                </div>
              </>
            )}

            {/* External Charge Fields */}
            {newCharge.isExternal && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="prestataireName">Prestataire *</Label>
                  <Input
                    id="prestataireName"
                    value={newCharge.prestataireName}
                    onChange={(e) => handleChargeInputChange('prestataireName', e.target.value)}
                    placeholder="Ex: Cabinet Expert"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="serviceDescription">Service *</Label>
                  <Input
                    id="serviceDescription"
                    value={newCharge.serviceDescription}
                    onChange={(e) => handleChargeInputChange('serviceDescription', e.target.value)}
                    placeholder="Ex: Analyse technique approfondie"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="costPerUnit">Coût total (€) *</Label>
                  <Input
                    id="costPerUnit"
                    type="number"
                    step="0.01"
                    value={newCharge.costPerUnit}
                    onChange={(e) => handleChargeInputChange('costPerUnit', e.target.value)}
                    placeholder="2500.00"
                  />
                </div>
              </>
            )}

            {/* Currency Selection */}
            <div className="space-y-2">
              <Label>Devise</Label>
              <Select
                value={newCharge.currency}
                onValueChange={(value) => handleChargeInputChange('currency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="EUR">EUR (€)</SelectItem>
                  <SelectItem value="USD">USD ($)</SelectItem>
                  <SelectItem value="GBP">GBP (£)</SelectItem>
                  <SelectItem value="XOF">XOF (CFA)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Cost Preview */}
            {((newCharge.costPerUnit && newCharge.duration && !newCharge.isExternal) ||
              (newCharge.costPerUnit && newCharge.isExternal)) && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-sm font-medium text-blue-800">
                  Coût total: {formatCurrency(
                    newCharge.isExternal
                      ? parseFloat(newCharge.costPerUnit || 0)
                      : parseFloat(newCharge.costPerUnit || 0) * parseFloat(newCharge.duration || 0)
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsChargeDialogOpen(false);
                setEditingCharge(null);
              }}
            >
              Annuler
            </Button>
            <Button onClick={handleSaveCharge}>
              {editingCharge ? 'Mettre à jour' : 'Ajouter'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default DepensesTab;