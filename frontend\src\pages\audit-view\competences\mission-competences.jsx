import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Target,
  Search,
  Filter,
  Plus,
  BookOpen,
  Star,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Edit,
  Trash2,
  X
} from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const MissionCompetences = () => {
  const [missions, setMissions] = useState([]);
  const [competences, setCompetences] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMission, setSelectedMission] = useState(null);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMissionSkill, setSelectedMissionSkill] = useState(null);
  const [assignFormData, setAssignFormData] = useState({
    skillId: '',
    requiredLevel: 3,
    importance: 'IMPORTANT'
  });
  const [editFormData, setEditFormData] = useState({
    requiredLevel: 3,
    importance: 'IMPORTANT'
  });

  // Fetch data from API
  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch missions with skills and available skills in parallel
      const [missionsResponse, skillsResponse] = await Promise.all([
        axios.get(`${getApiBaseUrl()}/audit/mission-skills`, { withCredentials: true }),
        axios.get(`${getApiBaseUrl()}/audit/skills`, { withCredentials: true })
      ]);

      if (missionsResponse.data.success) {
        const missionsData = missionsResponse.data.data;
        setMissions(missionsData);
        
        // Auto-select first mission if none selected
        if (missionsData.length > 0 && !selectedMission) {
          setSelectedMission(missionsData[0]);
        } else if (selectedMission) {
          // Update selected mission with fresh data
          const updatedMission = missionsData.find(m => m.id === selectedMission.id);
          if (updatedMission) {
            setSelectedMission(updatedMission);
          }
        }
      }

      if (skillsResponse.data.success) {
        setCompetences(skillsResponse.data.data);
      }

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  // Get mission statistics
  const getMissionStats = () => {
    const totalMissions = missions.length;
    const missionsWithSkills = missions.filter(m => m.requiredSkills && m.requiredSkills.length > 0).length;
    const totalRequiredSkills = missions.reduce((sum, m) => sum + (m.requiredSkills ? m.requiredSkills.length : 0), 0);
    const averageSkillsPerMission = totalMissions > 0 ? (totalRequiredSkills / totalMissions).toFixed(1) : 0;

    return {
      totalMissions,
      missionsWithSkills,
      totalRequiredSkills,
      averageSkillsPerMission
    };
  };

  // Filter missions based on search
  const filteredMissions = missions.filter(mission =>
    mission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    mission.code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get available skills for assignment (not already assigned to selected mission)
  const getAvailableSkills = () => {
    if (!selectedMission) return competences;
    
    const assignedSkillIds = selectedMission.requiredSkills?.map(rs => rs.skillId) || [];
    return competences.filter(skill => !assignedSkillIds.includes(skill.id));
  };

  // Assign skill to mission
  const handleAssignSkill = async () => {
    if (!assignFormData.skillId) {
      toast.error('Veuillez sélectionner une compétence');
      return;
    }

    try {
      const response = await axios.post(`${getApiBaseUrl()}/audit/mission-skills/${selectedMission.id}/skills`, {
        skillId: assignFormData.skillId,
        requiredLevel: assignFormData.requiredLevel,
        importance: assignFormData.importance
      }, {
        withCredentials: true
      });

      if (response.data.success) {
        const assignedSkill = competences.find(skill => skill.id === parseInt(assignFormData.skillId));
        const skillName = assignedSkill ? assignedSkill.name : 'Compétence';
        
        toast.success(`${skillName} ajoutée à la mission ${selectedMission.name}`);
        
        // Close modal and reset form
        setIsAssignModalOpen(false);
        setAssignFormData({ skillId: '', requiredLevel: 3, importance: 'IMPORTANT' });
        
        // Refresh data
        await fetchData();
      }
    } catch (error) {
      console.error('Error assigning skill:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de l\'ajout de la compétence');
    }
  };

  // Open edit modal
  const openEditModal = (missionSkill) => {
    setSelectedMissionSkill(missionSkill);
    setEditFormData({
      requiredLevel: missionSkill.requiredLevel,
      importance: missionSkill.importance
    });
    setIsEditModalOpen(true);
  };

  // Update mission skill requirement
  const handleUpdateMissionSkill = async () => {
    try {
      const response = await axios.put(
        `${getApiBaseUrl()}/audit/mission-skills/${selectedMission.id}/skills/${selectedMissionSkill.skillId}`,
        {
          requiredLevel: editFormData.requiredLevel,
          importance: editFormData.importance
        },
        { withCredentials: true }
      );

      if (response.data.success) {
        const skillName = selectedMissionSkill.skill?.name || 'Compétence';
        toast.success(`Exigence pour "${skillName}" mise à jour`);
        
        // Close modal and reset form
        setIsEditModalOpen(false);
        setSelectedMissionSkill(null);
        setEditFormData({ requiredLevel: 3, importance: 'IMPORTANT' });
        
        // Refresh data
        await fetchData();
      }
    } catch (error) {
      console.error('Error updating mission skill:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de la mise à jour');
    }
  };

  // Remove skill from mission
  const handleRemoveSkill = async (skillId, skillName) => {
    if (!confirm(`Êtes-vous sûr de vouloir retirer "${skillName}" des compétences requises pour cette mission ?`)) {
      return;
    }

    try {
      const response = await axios.delete(
        `${getApiBaseUrl()}/audit/mission-skills/${selectedMission.id}/skills/${skillId}`,
        { withCredentials: true }
      );

      if (response.data.success) {
        toast.success(`"${skillName}" retirée de la mission`);
        await fetchData();
      }
    } catch (error) {
      console.error('Error removing skill:', error);
      toast.error(error.response?.data?.message || 'Erreur lors du retrait de la compétence');
    }
  };

  // Get importance badge variant
  const getImportanceBadge = (importance) => {
    switch (importance) {
      case 'CRITICAL':
        return { variant: 'destructive', icon: AlertTriangle, text: 'Critique' };
      case 'IMPORTANT':
        return { variant: 'default', icon: Star, text: 'Important' };
      case 'NICE_TO_HAVE':
        return { variant: 'secondary', icon: CheckCircle, text: 'Souhaitable' };
      default:
        return { variant: 'default', icon: Star, text: importance };
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
        <span className="ml-2 text-gray-600">Chargement des missions...</span>
      </div>
    );
  }

  const missionStats = getMissionStats();

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6 border border-purple-100">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Target className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Compétences des Missions
            </h1>
            <p className="text-gray-600 text-sm">
              Gérez les compétences requises pour chaque mission d'audit
            </p>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <Target className="h-5 w-5 text-purple-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Total Missions</p>
                <p className="text-base font-bold text-gray-900">{missionStats.totalMissions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Avec Compétences</p>
                <p className="text-base font-bold text-gray-900">{missionStats.missionsWithSkills}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Total Exigences</p>
                <p className="text-base font-bold text-gray-900">{missionStats.totalRequiredSkills}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <Star className="h-5 w-5 text-orange-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Moy. par Mission</p>
                <p className="text-base font-bold text-gray-900">{missionStats.averageSkillsPerMission}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5" />
            Recherche et Filtres
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Rechercher une mission..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content - Master Detail Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Panel - Mission List */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-5 w-5" />
                Missions ({filteredMissions.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-[600px] overflow-y-auto">
                {filteredMissions.map((mission) => (
                  <div
                    key={mission.id}
                    onClick={() => setSelectedMission(mission)}
                    className={`p-4 border-b cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedMission?.id === mission.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 text-sm">
                          {mission.name}
                        </h3>
                        {mission.code && (
                          <p className="text-xs text-gray-500 mt-1">
                            Code: {mission.code}
                          </p>
                        )}
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {mission.etat || 'Planifiée'}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {mission.requiredSkills?.length || 0} compétences
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - Mission Skills Details */}
        <div className="lg:col-span-2">
          {selectedMission ? (
            <Card className="h-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl">
                      {selectedMission.name}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {selectedMission.code && `Code: ${selectedMission.code} • `}
                      État: {selectedMission.etat || 'Planifiée'}
                    </CardDescription>
                  </div>

                  <Button
                    onClick={() => setIsAssignModalOpen(true)}
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter une compétence
                  </Button>
                </div>

                {/* Mission Statistics */}
                <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {selectedMission.requiredSkills?.length || 0}
                    </div>
                    <div className="text-sm text-gray-500">Compétences Requises</div>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {selectedMission.requiredSkills?.filter(rs => rs.importance === 'CRITICAL').length || 0}
                    </div>
                    <div className="text-sm text-gray-500">Critiques</div>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {selectedMission.requiredSkills?.length > 0
                        ? (selectedMission.requiredSkills.reduce((sum, rs) => sum + rs.requiredLevel, 0) / selectedMission.requiredSkills.length).toFixed(1)
                        : 0}
                    </div>
                    <div className="text-sm text-gray-500">Niveau Moyen</div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <BookOpen className="h-5 w-5" />
                      Compétences Requises
                    </h3>
                    <Badge variant="outline">
                      {selectedMission.requiredSkills?.length || 0} compétences
                    </Badge>
                  </div>

                  <div className="max-h-[400px] overflow-y-auto space-y-3">
                    {selectedMission.requiredSkills && selectedMission.requiredSkills.length > 0 ? (
                      selectedMission.requiredSkills.map((missionSkill) => {
                        const importanceBadge = getImportanceBadge(missionSkill.importance);
                        const ImportanceIcon = importanceBadge.icon;

                        return (
                          <div key={missionSkill.skillId} className="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="text-base font-medium text-gray-900">
                                    {missionSkill.skill?.name || 'Compétence inconnue'}
                                  </h4>
                                  <Badge variant={importanceBadge.variant} className="text-xs">
                                    <ImportanceIcon className="h-3 w-3 mr-1" />
                                    {importanceBadge.text}
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-3">
                                  <span className="text-sm text-gray-600">Niveau requis:</span>
                                  <div className="flex items-center gap-1">
                                    {[1, 2, 3, 4, 5].map((star) => (
                                      <Star
                                        key={star}
                                        className={`h-4 w-4 ${
                                          star <= missionSkill.requiredLevel
                                            ? 'text-yellow-400 fill-current'
                                            : 'text-gray-300'
                                        }`}
                                      />
                                    ))}
                                    <span className="text-sm font-medium ml-1">
                                      {missionSkill.requiredLevel}/5
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  onClick={() => openEditModal(missionSkill)}
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0 text-blue-600 hover:text-blue-800"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  onClick={() => handleRemoveSkill(missionSkill.skillId, missionSkill.skill?.name)}
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0 text-red-600 hover:text-red-800"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-12">
                        <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h4 className="text-lg font-medium text-gray-900 mb-2">Aucune compétence requise</h4>
                        <p className="text-gray-600 mb-4">
                          Cette mission n'a pas encore de compétences requises définies.
                        </p>
                        <Button
                          onClick={() => setIsAssignModalOpen(true)}
                          className="bg-purple-600 hover:bg-purple-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Ajouter la première compétence
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="h-full">
              <CardContent className="flex items-center justify-center h-full p-12">
                <div className="text-center">
                  <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-gray-900 mb-2">Sélectionnez une mission</h3>
                  <p className="text-gray-600">
                    Choisissez une mission dans la liste de gauche pour gérer ses compétences requises.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Add Skill Modal */}
      <Dialog open={isAssignModalOpen} onOpenChange={setIsAssignModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Ajouter une compétence requise</DialogTitle>
            <DialogDescription>
              Définissez une nouvelle compétence requise pour la mission "{selectedMission?.name}"
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="skill" className="text-right">Compétence</Label>
              <Select
                value={assignFormData.skillId}
                onValueChange={(value) => setAssignFormData({ ...assignFormData, skillId: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Sélectionner une compétence" />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableSkills().map((skill) => (
                    <SelectItem key={skill.id} value={skill.id.toString()}>
                      {skill.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="requiredLevel" className="text-right">Niveau requis</Label>
              <Select
                value={assignFormData.requiredLevel.toString()}
                onValueChange={(value) => setAssignFormData({ ...assignFormData, requiredLevel: parseInt(value) })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-3 w-3 ${
                                star <= level ? 'text-yellow-400 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span>Niveau {level}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="importance" className="text-right">Importance</Label>
              <Select
                value={assignFormData.importance}
                onValueChange={(value) => setAssignFormData({ ...assignFormData, importance: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CRITICAL">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <span>Critique</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="IMPORTANT">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-blue-500" />
                      <span>Important</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="NICE_TO_HAVE">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Souhaitable</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignModalOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleAssignSkill}>
              Ajouter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Skill Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Modifier l'exigence de compétence</DialogTitle>
            <DialogDescription>
              Modifiez les exigences pour "{selectedMissionSkill?.skill?.name}"
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="editRequiredLevel" className="text-right">Niveau requis</Label>
              <Select
                value={editFormData.requiredLevel.toString()}
                onValueChange={(value) => setEditFormData({ ...editFormData, requiredLevel: parseInt(value) })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-3 w-3 ${
                                star <= level ? 'text-yellow-400 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span>Niveau {level}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="editImportance" className="text-right">Importance</Label>
              <Select
                value={editFormData.importance}
                onValueChange={(value) => setEditFormData({ ...editFormData, importance: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CRITICAL">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <span>Critique</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="IMPORTANT">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-blue-500" />
                      <span>Important</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="NICE_TO_HAVE">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Souhaitable</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleUpdateMissionSkill}>
              Mettre à jour
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MissionCompetences;
