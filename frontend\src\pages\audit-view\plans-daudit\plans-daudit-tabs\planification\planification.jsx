import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, X, ChevronUp, ChevronDown, BarChart3 } from "lucide-react";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { toast } from "sonner";
import { useApiRequest, withApiErrorHandling } from '@/hooks/useApiRequest';
import userService from "@/services/userService";
import MissionDetailsModal from "@/components/audit-view/mission-details-modal";

// Custom Gantt Chart Component for Missions
function CustomMissionGanttChart({ missions, periode, onMissionClick, selectedMission }) {
  // Modal state for mission details
  const [showMissionModal, setShowMissionModal] = useState(false);
  const [modalMission, setModalMission] = useState(null);

  // Handle mission click
  const handleMissionClick = (mission) => {
    setModalMission(mission);
    setShowMissionModal(true);
    if (onMissionClick) {
      onMissionClick(mission);
    }
  };

  // Helper function to format dates for Gantt
  const formatDateForGantt = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  // Calculate progress percentage
  const calculateProgress = (mission) => {
    if (mission.avancement !== undefined && mission.avancement !== null) {
      if (typeof mission.avancement === 'string') {
        const match = mission.avancement.match(/(\d+)/);
        if (match) {
          return parseInt(match[1]);
        }
      }
    }
    return mission.etat === 'Completed' ? 100 : mission.etat === 'In Progress' ? 50 : 0;
  };

  // Calculate bar position and width
  const calculateBarPosition = (mission, timeline) => {
    const startDate = formatDateForGantt(mission.datedebut);
    const endDate = formatDateForGantt(mission.datefin);

    if (!startDate || !endDate || timeline.length === 0) return null;

    const start = new Date(startDate);
    const end = new Date(endDate);
    const timelineStart = timeline[0];
    const timelineEnd = timeline[timeline.length - 1];

    const totalDuration = timelineEnd.getTime() - timelineStart.getTime();
    const missionStart = Math.max(start.getTime(), timelineStart.getTime());
    const missionEnd = Math.min(end.getTime(), timelineEnd.getTime());

    if (missionStart >= missionEnd) return null;

    const left = ((missionStart - timelineStart.getTime()) / totalDuration) * 100;
    const width = ((missionEnd - missionStart) / totalDuration) * 100;

    return { left: Math.max(0, left), width: Math.max(1, width) };
  };

  // Generate timeline dates based on period
  const generateTimeline = () => {
    if (missions.length === 0) return [];

    // Find min and max dates
    const dates = missions
      .map(mission => [
        formatDateForGantt(mission.datedebut),
        formatDateForGantt(mission.datefin)
      ])
      .flat()
      .filter(Boolean)
      .map(date => new Date(date));

    if (dates.length === 0) return [];

    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));

    // Adjust start date to beginning of period
    const startDate = new Date(minDate);
    const endDate = new Date(maxDate);

    switch (periode) {
      case 'Mois':
        // Start from the beginning of the month
        startDate.setDate(1);
        // Extend to end of month for the last mission
        endDate.setMonth(endDate.getMonth() + 1);
        endDate.setDate(0); // Last day of previous month
        break;
      case 'Trimestre':
        // Start from beginning of the month
        startDate.setDate(1);
        // Extend a bit for better view
        endDate.setMonth(endDate.getMonth() + 2);
        break;
      case 'Année':
        // Start from beginning of the year
        startDate.setMonth(0, 1);
        // Extend to end of year
        endDate.setMonth(11, 31);
        break;
    }

    // Generate timeline based on period
    const timeline = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      timeline.push(new Date(current));

      switch (periode) {
        case 'Mois':
          current.setDate(current.getDate() + 1); // Daily for month view
          break;
        case 'Trimestre':
          current.setMonth(current.getMonth() + 1); // Monthly for trimestre view
          break;
        case 'Année':
          current.setMonth(current.getMonth() + 3); // Quarterly for year view
          break;
        default:
          current.setDate(current.getDate() + 1);
      }
    }

    return timeline;
  };

  // Format timeline header labels
  const formatTimelineLabel = (date) => {
    switch (periode) {
      case 'Mois':
        return date.getDate();
      case 'Trimestre':
        return date.toLocaleDateString('fr-FR', { month: 'short' });
      case 'Année':
        return `T${Math.ceil((date.getMonth() + 1) / 3)}`;
      default:
        return date.getDate();
    }
  };

  // Generate comprehensive headers for better context
  const generateHeaders = (timeline) => {
    const headers = [];

    switch (periode) {
      case 'Mois': {
        // Month headers
        let currentMonth = null;
        let monthStart = 0;

        timeline.forEach((date, index) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          const monthKey = `${year}-${month}`;

          if (currentMonth !== monthKey) {
            if (currentMonth !== null) {
              headers.push({
                label: new Date(timeline[monthStart]).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }),
                start: monthStart,
                width: index - monthStart,
                type: 'month'
              });
            }
            currentMonth = monthKey;
            monthStart = index;
          }
        });

        // Add the last month
        if (currentMonth !== null) {
          headers.push({
            label: new Date(timeline[monthStart]).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }),
            start: monthStart,
            width: timeline.length - monthStart,
            type: 'month'
          });
        }
        break;
      }

      case 'Trimestre': {
        // Year headers for trimestre (since we show months, we group by year)
        let currentTrimestreYear = null;
        let yearStart = 0;

        timeline.forEach((date, index) => {
          const year = date.getFullYear();

          if (currentTrimestreYear !== year) {
            if (currentTrimestreYear !== null) {
              headers.push({
                label: currentTrimestreYear.toString(),
                start: yearStart,
                width: index - yearStart,
                type: 'year'
              });
            }
            currentTrimestreYear = year;
            yearStart = index;
          }
        });

        if (currentTrimestreYear !== null) {
          headers.push({
            label: currentTrimestreYear.toString(),
            start: yearStart,
            width: timeline.length - yearStart,
            type: 'year'
          });
        }
        break;
      }

      case 'Année': {
        // Year headers for année
        let currentAnneeYear = null;
        let anneeYearStart = 0;

        timeline.forEach((date, index) => {
          const year = date.getFullYear();

          if (currentAnneeYear !== year) {
            if (currentAnneeYear !== null) {
              headers.push({
                label: currentAnneeYear.toString(),
                start: anneeYearStart,
                width: index - anneeYearStart,
                type: 'year'
              });
            }
            currentAnneeYear = year;
            anneeYearStart = index;
          }
        });

        if (currentAnneeYear !== null) {
          headers.push({
            label: currentAnneeYear.toString(),
            start: anneeYearStart,
            width: timeline.length - anneeYearStart,
            type: 'year'
          });
        }
        break;
      }
    }

    return headers;
  };

  const timeline = generateTimeline();
  const headers = generateHeaders(timeline);
  const cellWidth = periode === 'Mois' ? 25 : periode === 'Trimestre' ? 40 : 80;

  if (timeline.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Aucune mission avec des dates valides
      </div>
    );
  }

  return (
    <>
      <div className="custom-gantt-container">
        <div className="flex">
          {/* Mission Names Column */}
          <div className="custom-gantt-missions" style={{ minWidth: '200px', maxWidth: '200px' }}>
            {/* Context Header (if applicable) */}
            {headers.length > 0 && (
              <div className="custom-gantt-context-header" style={{ height: '30px', borderBottom: '1px solid #e2e8f0', background: '#f1f5f9', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', fontWeight: '600', color: '#64748b' }}>
                {headers[0]?.type === 'month' ? 'Mois' : 'Année'}
              </div>
            )}
            {/* Header */}
            <div className="custom-gantt-header" style={{ height: '40px', borderBottom: '2px solid #e2e8f0', background: '#f8fafc', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: '600', fontSize: '14px' }}>
              Missions
            </div>
            {/* Mission Rows */}
            {missions.map((mission, index) => (
              <div
                key={mission.id}
                className={`custom-gantt-mission-row ${selectedMission?.id === mission.id ? 'selected' : ''}`}
                style={{
                  height: '40px',
                  borderBottom: '1px solid #e2e8f0',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '0 8px',
                  cursor: 'pointer',
                  backgroundColor: selectedMission?.id === mission.id ? '#dbeafe' : index % 2 === 0 ? '#ffffff' : '#f9fafb'
                }}
                onClick={() => handleMissionClick(mission)}
                title={mission.name}
              >
                <span className="text-sm text-gray-900 truncate">{mission.name}</span>
              </div>
            ))}
          </div>

          {/* Timeline and Bars */}
          <div className="custom-gantt-timeline" style={{ flex: 1, overflowX: 'auto', borderLeft: '1px solid #e2e8f0' }}>
            {/* Context Headers (if applicable) */}
            {headers.length > 0 && (
              <div className="custom-gantt-context-headers" style={{ height: '30px', borderBottom: '1px solid #e2e8f0', background: '#f1f5f9', display: 'flex', minWidth: `${timeline.length * cellWidth}px` }}>
                {headers.map((header, index) => (
                  <div
                    key={index}
                    className="custom-gantt-context-cell"
                    style={{
                      width: `${header.width * cellWidth}px`,
                      borderRight: '1px solid #cbd5e1',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '11px',
                      fontWeight: '600',
                      color: '#64748b',
                      textTransform: 'capitalize'
                    }}
                  >
                    {header.label}
                  </div>
                ))}
              </div>
            )}

            {/* Timeline Header */}
            <div className="custom-gantt-timeline-header" style={{ height: '40px', borderBottom: '2px solid #e2e8f0', background: '#f8fafc', display: 'flex', minWidth: `${timeline.length * cellWidth}px` }}>
              {timeline.map((date, index) => (
                <div
                  key={index}
                  className="custom-gantt-timeline-cell"
                  style={{
                    width: `${cellWidth}px`,
                    borderRight: '1px solid #e2e8f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}
                >
                  {formatTimelineLabel(date)}
                </div>
              ))}
            </div>

            {/* Mission Bars */}
            <div className="custom-gantt-bars" style={{ position: 'relative', minWidth: `${timeline.length * cellWidth}px` }}>
              {missions.map((mission, index) => {
                const barPosition = calculateBarPosition(mission, timeline);
                if (!barPosition) return null;

                const progress = calculateProgress(mission);

                return (
                  <div
                    key={mission.id}
                    className="custom-gantt-bar-row"
                    style={{
                      height: '40px',
                      borderBottom: '1px solid #e2e8f0',
                      position: 'relative',
                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#f9fafb'
                    }}
                  >
                    {/* Mission Bar */}
                    <div
                      className="custom-gantt-bar"
                      style={{
                        position: 'absolute',
                        left: `${(barPosition.left / 100) * timeline.length * cellWidth}px`,
                        width: `${Math.max(20, (barPosition.width / 100) * timeline.length * cellWidth)}px`,
                        height: '24px',
                        top: '8px',
                        backgroundColor: selectedMission?.id === mission.id ? '#3b82f6' : '#6366f1',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        overflow: 'hidden'
                      }}
                      onClick={() => handleMissionClick(mission)}
                      title={`${mission.name} (${progress}%)`}
                    >
                      {/* Progress Bar */}
                      <div
                        style={{
                          width: `${progress}%`,
                          height: '100%',
                          backgroundColor: selectedMission?.id === mission.id ? '#1d4ed8' : '#4f46e5',
                          borderRadius: '4px 0 0 4px'
                        }}
                      />
                      {/* Mission Name (if space allows) */}
                      <span
                        style={{
                          position: 'absolute',
                          left: '4px',
                          fontSize: '11px',
                          color: 'white',
                          fontWeight: '500',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          maxWidth: 'calc(100% - 8px)'
                        }}
                      >
                        {mission.name}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Mission Details Modal */}
      {showMissionModal && modalMission && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={() => setShowMissionModal(false)}>
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">Détails de la mission</h3>
                <button
                  onClick={() => setShowMissionModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nom de la mission</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalMission.name}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Catégorie</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalMission.categorie || 'Non renseignée'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {modalMission.datedebut ? new Date(modalMission.datedebut).toLocaleDateString('fr-FR') : 'Non définie'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {modalMission.datefin ? new Date(modalMission.datefin).toLocaleDateString('fr-FR') : 'Non définie'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">État</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalMission.etat || 'Non défini'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Avancement</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalMission.avancement || 'Non renseigné'}</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Objectif</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{modalMission.objectif || 'Non renseigné'}</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Progression</label>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-900">{calculateProgress(modalMission)}% complété</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${calculateProgress(modalMission)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              <div className="flex justify-end">
                <button
                  onClick={() => setShowMissionModal(false)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Fermer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

function PlanificationTab({ auditPlan }) {
  const { makeRequest, cancelAllRequests } = useApiRequest();

  // Filters
  const [periode, setPeriode] = useState("Mois");
  const [dateDebut, setDateDebut] = useState("");
  const [dateFin, setDateFin] = useState("");

  // State management
  const [missions, setMissions] = useState([]);
  const [selectedMission, setSelectedMission] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [users, setUsers] = useState([]);
  const [isGanttOpen, setIsGanttOpen] = useState(true);

  // Fetch missions data
  useEffect(() => {
    const fetchMissionsData = withApiErrorHandling(async () => {
      if (!auditPlan?.id) return;

      setIsLoading(true);

      try {
        // Fetch missions and users in parallel with cancellation support
        const [missionsResponse, usersResponse] = await Promise.all([
          makeRequest({
            method: 'get',
            url: `${getApiBaseUrl()}/audit-missions/plan/${auditPlan.id}`,
            withCredentials: true,
            headers: { 'Content-Type': 'application/json' }
          }, {
            retries: 2,
            onError: (error) => {
              if (!axios.isCancel(error)) {
                console.error("Error fetching missions:", error);
                toast.error("Erreur lors du chargement des missions");
              }
            }
          }),
          // Use user service instead of direct API call
          userService.fetchUsers({ silent: true })
        ]);

        if (missionsResponse && missionsResponse.data.success) {
          setMissions(missionsResponse.data.data);
        } else {
          setMissions([]);
        }

        // usersResponse is now the users array from userService
        if (usersResponse && Array.isArray(usersResponse)) {
          setUsers(usersResponse);
        } else {
          setUsers([]);
        }
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error("Error in parallel requests:", error);
        }
        setMissions([]);
        setUsers([]);
      }

      setIsLoading(false);
    }, {
      fallbackValue: null,
      autoRefresh: true, // Auto refresh on critical errors
      refreshDelay: 5000,
      onError: (error) => {
        setIsLoading(false);
        setMissions([]);
        setUsers([]);
        if (!axios.isCancel(error)) {
          console.error("Critical error fetching missions data:", error);
          toast.error("Erreur lors du chargement des données");
        }
      }
    });

    if (auditPlan?.id) {
      fetchMissionsData();
    }

    // Cleanup on unmount or dependency change
    return () => {
      cancelAllRequests();
    };
  }, [auditPlan?.id, makeRequest, cancelAllRequests]);

  // Helper function to format dates for Gantt
  const formatDateForGantt = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  // Helper function to handle mission click
  const handleMissionClick = (mission) => {
    setSelectedMission(mission);
    setIsModalOpen(true);
  };

  if (!auditPlan) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
      </div>
    );
  }



  // Filter missions for display in the list
  const filteredMissions = missions.filter(mission => {
    const startDate = formatDateForGantt(mission.datedebut);
    const endDate = formatDateForGantt(mission.datefin);

    // Skip missions without valid dates
    if (!startDate || !endDate) return false;

    // Apply date filters
    if (dateDebut && endDate < dateDebut) return false;
    if (dateFin && startDate > dateFin) return false;

    return true;
  });

  return (
    <div className="space-y-4 py-4">
      {/* Loading state */}
      {isLoading && (
        <div className="text-center py-10">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-500">Chargement des missions...</p>
        </div>
      )}

      {/* Gantt Section */}
      {!isLoading && (
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="border rounded-lg shadow-sm">
            <button
              type="button"
              className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
              onClick={() => setIsGanttOpen(!isGanttOpen)}
            >
              <div className="flex items-center gap-2">
                {isGanttOpen ? (
                  <ChevronUp className="h-5 w-5 text-blue-600" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-blue-600" />
                )}
                <BarChart3 className="h-5 w-5 text-blue-600 mr-1" />
                <span className="text-lg font-medium text-blue-800">Diagramme de Gantt</span>
              </div>
            </button>

            {isGanttOpen && (
              <div className="p-5 bg-white">
                {/* Filter Options */}
                <div className="flex flex-col md:flex-row gap-4 justify-center items-center mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex flex-col items-start min-w-[180px]">
                    <label className="text-sm font-medium mb-1">Période de calendrier</label>
                    <Select value={periode} onValueChange={setPeriode}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Période" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Mois">Mois</SelectItem>
                        <SelectItem value="Trimestre">Trimestre</SelectItem>
                        <SelectItem value="Année">Année</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex flex-col items-start min-w-[180px]">
                    <label className="text-sm font-medium mb-1">Date de début</label>
                    <Input type="date" value={dateDebut} onChange={e => setDateDebut(e.target.value)} />
                  </div>
                  <div className="flex flex-col items-start min-w-[180px]">
                    <label className="text-sm font-medium mb-1">Date de fin</label>
                    <Input type="date" value={dateFin} onChange={e => setDateFin(e.target.value)} />
                  </div>
                </div>

                {/* Custom Gantt Chart */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="p-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">
                      Planification des Missions - {auditPlan.name}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {filteredMissions.length} mission{filteredMissions.length > 1 ? 's' : ''} affichée{filteredMissions.length > 1 ? 's' : ''}
                    </p>
                  </div>

                  {filteredMissions.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      Aucune mission à afficher pour la période sélectionnée
                    </div>
                  ) : (
                    <CustomMissionGanttChart
                      missions={filteredMissions}
                      periode={periode}
                      onMissionClick={handleMissionClick}
                      selectedMission={selectedMission}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Mission Details Modal */}
      <MissionDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        mission={selectedMission}
        users={users}
      />
    </div>
  );
}

export default PlanificationTab;