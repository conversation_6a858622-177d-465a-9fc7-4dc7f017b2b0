'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // First, check if the column exists and its current type
      const tableDescription = await queryInterface.describeTable('AllAttachment');

      if (tableDescription.questionID) {
        console.log('Updating AllAttachment.questionID from INTEGER to STRING...');

        // Step 1: Drop the foreign key constraint if it exists
        try {
          await queryInterface.removeConstraint('AllAttachment', 'AllAttachment_questionID_fkey');
          console.log('Dropped foreign key constraint AllAttachment_questionID_fkey');
        } catch (error) {
          console.log('Foreign key constraint may not exist or already dropped:', error.message);
        }

        // Step 2: Change the column type from INTEGER to STRING
        await queryInterface.changeColumn('AllAttachment', 'questionID', {
          type: Sequelize.STRING,
          allowNull: true
        });

        console.log('Successfully updated AllAttachment.questionID to STRING type');

        // Note: We don't re-add the foreign key constraint because we want to support both
        // integer IDs (from ControlQuestion) and string IDs (from Question)

      } else {
        console.log('questionID column does not exist in AllAttachment table');
      }
    } catch (error) {
      console.error('Error updating AllAttachment.questionID:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Revert back to INTEGER type
      // Note: This might fail if there are string values in the column
      console.log('Reverting AllAttachment.questionID back to INTEGER...');

      // First, clear any string values that can't be converted to integers
      await queryInterface.sequelize.query(
        "UPDATE \"AllAttachment\" SET \"questionID\" = NULL WHERE \"questionID\" ~ '[^0-9]'"
      );

      await queryInterface.changeColumn('AllAttachment', 'questionID', {
        type: Sequelize.INTEGER,
        allowNull: true
      });

      // Re-add the foreign key constraint
      try {
        await queryInterface.addConstraint('AllAttachment', {
          fields: ['questionID'],
          type: 'foreign key',
          name: 'AllAttachment_questionID_fkey',
          references: {
            table: 'ControlQuestion',
            field: 'id'
          },
          onDelete: 'SET NULL',
          onUpdate: 'CASCADE'
        });
        console.log('Re-added foreign key constraint');
      } catch (error) {
        console.log('Could not re-add foreign key constraint:', error.message);
      }

      console.log('Successfully reverted AllAttachment.questionID to INTEGER type');
    } catch (error) {
      console.error('Error reverting AllAttachment.questionID:', error);
      throw error;
    }
  }
};
