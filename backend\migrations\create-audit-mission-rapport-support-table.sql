-- Create AuditMissionRapportSupports table
CREATE TABLE IF NOT EXISTS "AuditMissionRapportSupports" (
    "id" VARCHAR(255) PRIMARY KEY NOT NULL,
    "logo" TEXT,
    "signatureElectrique" TEXT,
    "destinataire" TEXT,
    "auditMissionId" VARCHAR(255) NOT NULL,
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT "fk_audit_mission_rapport_support_mission" 
        FOREIGN KEY ("auditMissionId") 
        REFERENCES "AuditMissions"("id") 
        ON DELETE CASCADE ON UPDATE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_audit_mission_rapport_support_mission_id" 
    ON "AuditMissionRapportSupports"("auditMissionId");

-- Create unique constraint to ensure one rapport support per mission
CREATE UNIQUE INDEX IF NOT EXISTS "idx_audit_mission_rapport_support_unique_mission" 
    ON "AuditMissionRapportSupports"("auditMissionId");

-- Add comments for documentation
COMMENT ON TABLE "AuditMissionRapportSupports" IS 'Support elements for audit mission reports including logos, signatures, and recipients';
COMMENT ON COLUMN "AuditMissionRapportSupports"."id" IS 'Unique identifier for the rapport support';
COMMENT ON COLUMN "AuditMissionRapportSupports"."logo" IS 'Base64 encoded logo image or file path';
COMMENT ON COLUMN "AuditMissionRapportSupports"."signatureElectrique" IS 'Base64 encoded electronic signature image or file path';
COMMENT ON COLUMN "AuditMissionRapportSupports"."destinataire" IS 'Recipient information for the report';
COMMENT ON COLUMN "AuditMissionRapportSupports"."auditMissionId" IS 'Reference to the audit mission';
