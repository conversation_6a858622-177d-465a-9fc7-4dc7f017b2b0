'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('audit_mission_skills', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      missionId: {
        type: Sequelize.STRING,
        allowNull: false,
        references: {
          model: 'AuditMissions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      skillId: {
        type: Sequelize.STRING,
        allowNull: false,
        references: {
          model: 'AuditSkills',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      requiredLevel: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 5
        }
      },
      importance: {
        type: Sequelize.ENUM('CRITICAL', 'IMPORTANT', 'NICE_TO_HAVE'),
        allowNull: false,
        defaultValue: 'IMPORTANT'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint
    await queryInterface.addConstraint('audit_mission_skills', {
      fields: ['missionId', 'skillId'],
      type: 'unique',
      name: 'unique_mission_skill'
    });

    // Add indexes for better performance
    await queryInterface.addIndex('audit_mission_skills', ['missionId']);
    await queryInterface.addIndex('audit_mission_skills', ['skillId']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('audit_mission_skills');
  }
};
